cmake_minimum_required(VERSION 3.16)

project(PhotoEditor VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Gui)

# Enable Qt's MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Source files
set(SOURCES
    src/main.cpp
    src/mainwindow.cpp
    src/imagecanvas.cpp
    src/layermanager.cpp
    src/toolpalette.cpp
    src/imageprocessor.cpp
    src/projectmanager.cpp
    src/undocommands.cpp
)

# Header files
set(HEADERS
    src/mainwindow.h
    src/imagecanvas.h
    src/layermanager.h
    src/toolpalette.h
    src/imageprocessor.h
    src/projectmanager.h
    src/undocommands.h
)

# Resource files (optional - can be added later)
# set(RESOURCES
#     resources/resources.qrc
# )

# Create executable
add_executable(PhotoEditor
    ${SOURCES}
    ${HEADERS}
    # ${RESOURCES}  # Uncomment when resources are added
)

# Link Qt libraries
target_link_libraries(PhotoEditor
    Qt6::Core
    Qt6::Widgets
    Qt6::Gui
)

# Set target properties
set_target_properties(PhotoEditor PROPERTIES
    WIN32_EXECUTABLE TRUE
    MACOSX_BUNDLE TRUE
)

# Include directories
target_include_directories(PhotoEditor PRIVATE src)

# Compiler-specific options
if(MSVC)
    target_compile_options(PhotoEditor PRIVATE /W4)
else()
    target_compile_options(PhotoEditor PRIVATE -Wall -Wextra -Wpedantic)
endif()
