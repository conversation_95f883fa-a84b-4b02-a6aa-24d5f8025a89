# PhotoEditor - Qt C++ Photo Editing Application

A comprehensive photo editing application built with Qt C++ featuring a modern dark theme interface, layer management, and professional image editing tools.

## Features

### Core Application Features
- **Project Management**: Create, save, and load photo editing projects
- **Comprehensive Menu System**: File, Edit, View, Tools, and Help menus with full functionality
- **Undo/Redo System**: Complete undo/redo support for all operations
- **Zoom and Pan**: Smooth zooming and panning with mouse wheel and drag support
- **Layer Management**: Full layer system with visibility, opacity, and blend modes
- **Tool Palette**: Professional tool selection with customizable brush settings
- **Status Bar**: Real-time display of zoom level, cursor position, and image dimensions
- **Dock System**: Resizable and dockable panels for tools, layers, history, and properties

### Image Editing Features
- **Import/Export**: Support for common image formats (PNG, JPEG, BMP, TIFF)
- **Color Adjustments**: Brightness, contrast, saturation, and hue controls
- **Crop Tool**: Interactive cropping with selection rectangle
- **Resize Tool**: Image resizing with aspect ratio options
- **Filters**: Blur, sharpen, edge detection, emboss effects
- **Color Effects**: Grayscale, sepia, invert, posterize
- **Advanced Processing**: Histogram operations, noise reduction, custom convolution filters

### User Interface
- **Dark Theme**: Modern dark interface with white text and rounded corners
- **Responsive Layout**: Adaptive UI that works well at different window sizes
- **Keyboard Shortcuts**: Standard shortcuts for common operations
- **Context Menus**: Right-click context menus for quick access to tools
- **Progress Indicators**: Visual feedback for long-running operations

## Project Structure

```
PhotoEditor/
├── src/                    # Source files
│   ├── main.cpp           # Application entry point
│   ├── mainwindow.h/cpp   # Main application window
│   ├── imagecanvas.h/cpp  # Core image editing widget
│   ├── layermanager.h/cpp # Layer management system
│   ├── toolpalette.h/cpp  # Tool selection and settings
│   ├── projectmanager.h/cpp # Project save/load functionality
│   ├── undocommands.h/cpp # Undo/redo command system
│   └── imageprocessor.h/cpp # Image processing algorithms
├── ui/                    # UI form files (optional)
├── resources/             # Application resources
├── CMakeLists.txt         # CMake build configuration
├── PhotoEditor.pro        # qmake build configuration
└── README.md             # This file
```

## Requirements

### System Requirements
- Windows 10/11, macOS 10.15+, or Linux (Ubuntu 18.04+)
- Qt 6.2 or later
- C++17 compatible compiler
- CMake 3.16+ or qmake

### Qt Modules Required
- Qt6Core
- Qt6Widgets  
- Qt6Gui

## Building the Application

### Option 1: Using Qt Creator (Recommended)
1. Install Qt Creator and Qt 6.2+
2. Open `PhotoEditor.pro` in Qt Creator
3. Configure the project with your Qt kit
4. Build and run the project

### Option 2: Using CMake
```bash
# Create build directory
mkdir build
cd build

# Configure with CMake
cmake -DCMAKE_PREFIX_PATH=/path/to/Qt6 ..

# Build the project
cmake --build .

# Run the application
./PhotoEditor  # Linux/macOS
PhotoEditor.exe  # Windows
```

### Option 3: Using qmake
```bash
# Generate Makefile
qmake PhotoEditor.pro

# Build the project
make  # Linux/macOS
nmake  # Windows with MSVC
mingw32-make  # Windows with MinGW

# Run the application
./PhotoEditor  # Linux/macOS
PhotoEditor.exe  # Windows
```

## Installation

### Windows
1. Build the application using one of the methods above
2. Copy the executable and required Qt DLLs to your desired location
3. Optionally create a desktop shortcut

### macOS
1. Build the application
2. The build process creates a PhotoEditor.app bundle
3. Copy to Applications folder or run directly

### Linux
1. Build the application
2. Install to system path or run from build directory
3. Ensure Qt libraries are available in system path

## Usage

### Getting Started
1. Launch PhotoEditor
2. Create a new project or import an existing image
3. Use the tool palette to select editing tools
4. Apply adjustments using the properties panel
5. Manage layers using the layer panel
6. Save your project for future editing

### Keyboard Shortcuts
- `Ctrl+N`: New project
- `Ctrl+O`: Open project
- `Ctrl+S`: Save project
- `Ctrl+Shift+S`: Save project as
- `Ctrl+I`: Import image
- `Ctrl+E`: Export image
- `Ctrl+Z`: Undo
- `Ctrl+Y`: Redo
- `Ctrl+C`: Copy
- `Ctrl+V`: Paste
- `+/-`: Zoom in/out
- `Ctrl+0`: Fit to window
- `Ctrl+1`: Actual size

### Tool Shortcuts
- `S`: Select tool
- `M`: Move tool
- `C`: Crop tool
- `B`: Paint brush
- `E`: Eraser
- `H`: Pan tool
- `Z`: Zoom tool
- `I`: Eyedropper

## Architecture

The application follows a modular architecture with clear separation of concerns:

- **MainWindow**: Central application window and UI coordination
- **ImageCanvas**: Core image display and editing functionality
- **LayerManager**: Layer system management and compositing
- **ToolPalette**: Tool selection and configuration
- **ProjectManager**: Project persistence and file I/O
- **UndoCommands**: Command pattern implementation for undo/redo
- **ImageProcessor**: Low-level image processing algorithms

## Contributing

This is a demonstration project showcasing Qt C++ development best practices. The codebase includes:

- Modern C++17 features
- Qt's signal-slot mechanism
- Model-View architecture
- Command pattern for undo/redo
- Custom widget development
- Resource management
- Cross-platform compatibility

## License

This project is provided as-is for educational and demonstration purposes.

## Technical Notes

### Performance Considerations
- Image processing operations are optimized for real-time feedback
- Large images are handled efficiently with proper memory management
- Layer compositing uses Qt's optimized painting system

### Extensibility
The modular design allows for easy extension:
- Add new tools by extending the ToolPalette
- Implement new filters in ImageProcessor
- Add new layer blend modes in LayerManager
- Extend file format support in ProjectManager

### Qt Creator Integration
The project is fully compatible with Qt Creator IDE:
- Syntax highlighting and code completion
- Integrated debugging support
- UI designer integration (for future UI files)
- Built-in help system integration
