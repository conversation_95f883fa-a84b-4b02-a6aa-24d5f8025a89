#include "imagecanvas.h"
#include <QMouseEvent>
#include <QWheelEvent>
#include <QPaintEvent>
#include <QKeyEvent>
#include <QPainter>
#include <QScrollBar>
#include <QApplication>
#include <QClipboard>
#include <QMimeData>
#include <QRubberBand>
#include <QUndoStack>
#include <QFileInfo>
#include <QImageReader>
#include <QImageWriter>
#include <QMessageBox>
#include <QInputDialog>
#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QSpinBox>
#include <QPushButton>
#include <QGroupBox>
#include <QSlider>
#include <QComboBox>
#include <QCheckBox>
#include <QGridLayout>
#include <cmath>

// Constants
const double ImageCanvas::MinZoomFactor = 0.1;
const double ImageCanvas::MaxZoomFactor = 10.0;
const double ImageCanvas::ZoomStep = 1.2;

ImageCanvas::ImageCanvas(QWidget *parent)
    : QWidget(parent)
    , m_zoomFactor(1.0)
    , m_imageOffset(0, 0)
    , m_lastPanPoint(0, 0)
    , m_isPanning(false)
    , m_hasSelection(false)
    , m_rubberBand(nullptr)
    , m_selectionStart(0, 0)
    , m_isSelecting(false)
    , m_currentTool(SelectTool)
    , m_isDrawing(false)
    , m_undoStack(nullptr)
{
    setFocusPolicy(Qt::StrongFocus);
    setMouseTracking(true);
    setMinimumSize(400, 300);
    
    // Create rubber band for selection
    m_rubberBand = new QRubberBand(QRubberBand::Rectangle, this);
    
    // Set background color
    setStyleSheet("background-color: #2b2b2b;");
}

ImageCanvas::~ImageCanvas()
{
    delete m_rubberBand;
}

bool ImageCanvas::loadImage(const QString &fileName)
{
    QImageReader reader(fileName);
    reader.setAutoTransform(true);
    const QImage image = reader.read();
    
    if (image.isNull()) {
        return false;
    }
    
    m_image = QPixmap::fromImage(image);
    m_originalImage = m_image;
    
    // Reset view
    m_zoomFactor = 1.0;
    m_imageOffset = QPoint(0, 0);
    m_hasSelection = false;
    m_rubberBand->hide();
    
    updateImageRect();
    centerImage();
    update();
    
    emit imageChanged();
    emit zoomChanged(m_zoomFactor);
    
    return true;
}

bool ImageCanvas::saveImage(const QString &fileName)
{
    if (m_image.isNull()) {
        return false;
    }
    
    QImageWriter writer(fileName);
    return writer.write(m_image.toImage());
}

void ImageCanvas::newImage(int width, int height)
{
    m_image = QPixmap(width, height);
    m_image.fill(Qt::white);
    m_originalImage = m_image;
    
    // Reset view
    m_zoomFactor = 1.0;
    m_imageOffset = QPoint(0, 0);
    m_hasSelection = false;
    m_rubberBand->hide();
    
    updateImageRect();
    centerImage();
    update();
    
    emit imageChanged();
    emit zoomChanged(m_zoomFactor);
}

void ImageCanvas::zoomIn()
{
    double newZoom = m_zoomFactor * ZoomStep;
    if (newZoom <= MaxZoomFactor) {
        setZoomFactor(newZoom);
    }
}

void ImageCanvas::zoomOut()
{
    double newZoom = m_zoomFactor / ZoomStep;
    if (newZoom >= MinZoomFactor) {
        setZoomFactor(newZoom);
    }
}

void ImageCanvas::zoomFit()
{
    if (m_image.isNull()) return;
    
    QSize imageSize = m_image.size();
    QSize widgetSize = size();
    
    double scaleX = static_cast<double>(widgetSize.width()) / imageSize.width();
    double scaleY = static_cast<double>(widgetSize.height()) / imageSize.height();
    
    double scale = qMin(scaleX, scaleY) * 0.9; // 90% to leave some margin
    scale = qBound(MinZoomFactor, scale, MaxZoomFactor);
    
    setZoomFactor(scale);
    centerImage();
}

void ImageCanvas::zoomActual()
{
    setZoomFactor(1.0);
    centerImage();
}

void ImageCanvas::setZoomFactor(double factor)
{
    factor = qBound(MinZoomFactor, factor, MaxZoomFactor);
    
    if (qAbs(factor - m_zoomFactor) > 0.001) {
        m_zoomFactor = factor;
        updateImageRect();
        update();
        emit zoomChanged(m_zoomFactor);
    }
}

void ImageCanvas::resetView()
{
    m_zoomFactor = 1.0;
    m_imageOffset = QPoint(0, 0);
    updateImageRect();
    centerImage();
    update();
    emit zoomChanged(m_zoomFactor);
}

void ImageCanvas::centerImage()
{
    if (m_image.isNull()) return;
    
    QSize scaledSize = m_image.size() * m_zoomFactor;
    QSize widgetSize = size();
    
    m_imageOffset.setX((widgetSize.width() - scaledSize.width()) / 2);
    m_imageOffset.setY((widgetSize.height() - scaledSize.height()) / 2);
    
    updateImageRect();
    update();
}

void ImageCanvas::selectAll()
{
    if (m_image.isNull()) return;
    
    m_hasSelection = true;
    m_selectionRect = QRect(0, 0, m_image.width(), m_image.height());
    updateSelection();
    update();
    emit selectionChanged();
}

void ImageCanvas::deselectAll()
{
    m_hasSelection = false;
    m_rubberBand->hide();
    update();
    emit selectionChanged();
}

void ImageCanvas::cut()
{
    if (m_hasSelection) {
        copy();
        // Fill selection with background color (white)
        QPainter painter(&m_image);
        painter.fillRect(m_selectionRect, Qt::white);
        update();
        emit imageChanged();
    }
}

void ImageCanvas::copy()
{
    if (m_hasSelection && !m_image.isNull()) {
        QPixmap selectedPixmap = m_image.copy(m_selectionRect);
        QApplication::clipboard()->setPixmap(selectedPixmap);
    }
}

void ImageCanvas::paste()
{
    const QClipboard *clipboard = QApplication::clipboard();
    const QMimeData *mimeData = clipboard->mimeData();
    
    if (mimeData->hasImage()) {
        QPixmap pixmap = clipboard->pixmap();
        if (!pixmap.isNull()) {
            // For simplicity, replace the entire image
            // In a full implementation, you'd paste at cursor position
            m_image = pixmap;
            m_originalImage = m_image;
            updateImageRect();
            centerImage();
            update();
            emit imageChanged();
        }
    }
}

void ImageCanvas::setCurrentTool(Tool tool)
{
    m_currentTool = tool;
    
    // Update cursor based on tool
    switch (tool) {
    case SelectTool:
        setCursor(Qt::CrossCursor);
        break;
    case CropTool:
        setCursor(Qt::CrossCursor);
        break;
    case PanTool:
        setCursor(Qt::OpenHandCursor);
        break;
    }
}

void ImageCanvas::activateCropTool()
{
    setCurrentTool(CropTool);
}

void ImageCanvas::updateImageRect()
{
    if (m_image.isNull()) {
        m_imageRect = QRect();
        return;
    }

    QSize scaledSize = m_image.size() * m_zoomFactor;
    m_imageRect = QRect(m_imageOffset, scaledSize);
}

void ImageCanvas::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // Fill background
    painter.fillRect(rect(), QColor(43, 43, 43));

    if (!m_image.isNull()) {
        drawImage(painter);
        drawSelection(painter);
    }

    // Draw grid if zoomed in enough
    if (m_zoomFactor > 4.0) {
        drawGrid(painter);
    }
}

void ImageCanvas::drawImage(QPainter &painter)
{
    if (m_image.isNull()) return;

    // Draw checkerboard background for transparency
    QRect imageRect = m_imageRect;
    painter.save();
    painter.setClipRect(imageRect);

    // Checkerboard pattern
    const int checkSize = 10;
    QColor lightGray(200, 200, 200);
    QColor darkGray(150, 150, 150);

    for (int x = imageRect.left(); x < imageRect.right(); x += checkSize) {
        for (int y = imageRect.top(); y < imageRect.bottom(); y += checkSize) {
            QRect checkRect(x, y, checkSize, checkSize);
            bool isLight = ((x / checkSize) + (y / checkSize)) % 2 == 0;
            painter.fillRect(checkRect, isLight ? lightGray : darkGray);
        }
    }

    painter.restore();

    // Draw the actual image
    painter.drawPixmap(m_imageRect, m_image);
}

void ImageCanvas::drawSelection(QPainter &painter)
{
    if (!m_hasSelection) return;

    QRect selectionWidget = imageToWidget(m_selectionRect);

    // Draw selection outline
    painter.save();
    QPen pen(Qt::white, 1, Qt::DashLine);
    painter.setPen(pen);
    painter.drawRect(selectionWidget);

    // Draw selection handles
    const int handleSize = 6;
    QBrush handleBrush(Qt::white);
    painter.setBrush(handleBrush);
    painter.setPen(Qt::black);

    // Corner handles
    painter.drawRect(QRect(selectionWidget.topLeft() - QPoint(handleSize/2, handleSize/2), QSize(handleSize, handleSize)));
    painter.drawRect(QRect(selectionWidget.topRight() - QPoint(handleSize/2, handleSize/2), QSize(handleSize, handleSize)));
    painter.drawRect(QRect(selectionWidget.bottomLeft() - QPoint(handleSize/2, handleSize/2), QSize(handleSize, handleSize)));
    painter.drawRect(QRect(selectionWidget.bottomRight() - QPoint(handleSize/2, handleSize/2), QSize(handleSize, handleSize)));

    painter.restore();
}

void ImageCanvas::drawGrid(QPainter &painter)
{
    if (m_image.isNull()) return;

    painter.save();
    painter.setPen(QPen(QColor(100, 100, 100), 1));

    int gridSpacing = static_cast<int>(m_zoomFactor * 10);
    if (gridSpacing < 5) return;

    QRect imageRect = m_imageRect;

    // Vertical lines
    for (int x = imageRect.left(); x <= imageRect.right(); x += gridSpacing) {
        painter.drawLine(x, imageRect.top(), x, imageRect.bottom());
    }

    // Horizontal lines
    for (int y = imageRect.top(); y <= imageRect.bottom(); y += gridSpacing) {
        painter.drawLine(imageRect.left(), y, imageRect.right(), y);
    }

    painter.restore();
}

void ImageCanvas::mousePressEvent(QMouseEvent *event)
{
    if (m_image.isNull()) return;

    if (event->button() == Qt::LeftButton) {
        QPoint imagePoint = widgetToImage(event->pos());

        switch (m_currentTool) {
        case SelectTool:
        case CropTool:
            if (m_imageRect.contains(event->pos())) {
                m_isSelecting = true;
                m_selectionStart = imagePoint;
                m_hasSelection = false;
                m_rubberBand->hide();
            }
            break;

        case PanTool:
            m_isPanning = true;
            m_lastPanPoint = event->pos();
            setCursor(Qt::ClosedHandCursor);
            break;
        }
    } else if (event->button() == Qt::MiddleButton) {
        // Middle button always pans
        m_isPanning = true;
        m_lastPanPoint = event->pos();
        setCursor(Qt::ClosedHandCursor);
    }
}

void ImageCanvas::mouseMoveEvent(QMouseEvent *event)
{
    if (m_image.isNull()) return;

    // Update position label
    QPoint imagePoint = widgetToImage(event->pos());
    if (m_imageRect.contains(event->pos())) {
        emit positionChanged(imagePoint);
    }

    if (m_isSelecting) {
        QPoint currentImagePoint = widgetToImage(event->pos());
        QRect selectionRect = QRect(m_selectionStart, currentImagePoint).normalized();

        // Clamp to image bounds
        selectionRect = selectionRect.intersected(QRect(0, 0, m_image.width(), m_image.height()));

        if (!selectionRect.isEmpty()) {
            m_selectionRect = selectionRect;
            m_hasSelection = true;
            updateSelection();
        }
    } else if (m_isPanning) {
        QPoint delta = event->pos() - m_lastPanPoint;
        m_imageOffset += delta;
        m_lastPanPoint = event->pos();
        updateImageRect();
        update();
    }
}

void ImageCanvas::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        if (m_isSelecting) {
            m_isSelecting = false;
            if (m_hasSelection) {
                emit selectionChanged();
            }
        }

        if (m_isPanning) {
            m_isPanning = false;
            setCurrentTool(m_currentTool); // Restore cursor
        }
    } else if (event->button() == Qt::MiddleButton) {
        if (m_isPanning) {
            m_isPanning = false;
            setCurrentTool(m_currentTool); // Restore cursor
        }
    }
}

void ImageCanvas::wheelEvent(QWheelEvent *event)
{
    if (event->modifiers() & Qt::ControlModifier) {
        // Zoom with Ctrl+Wheel
        const double scaleFactor = 1.15;
        if (event->angleDelta().y() > 0) {
            setZoomFactor(m_zoomFactor * scaleFactor);
        } else {
            setZoomFactor(m_zoomFactor / scaleFactor);
        }
        event->accept();
    } else {
        // Pan with wheel
        QPoint delta = event->angleDelta() / 8;
        m_imageOffset += delta;
        updateImageRect();
        update();
        event->accept();
    }
}

void ImageCanvas::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
    case Qt::Key_Delete:
    case Qt::Key_Backspace:
        if (m_hasSelection) {
            cut();
        }
        break;

    case Qt::Key_Escape:
        deselectAll();
        break;

    case Qt::Key_Space:
        if (!m_isPanning) {
            setCurrentTool(PanTool);
        }
        break;

    default:
        QWidget::keyPressEvent(event);
        break;
    }
}

void ImageCanvas::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    if (!m_image.isNull()) {
        updateImageRect();
        update();
    }
}

void ImageCanvas::updateSelection()
{
    if (!m_hasSelection) {
        m_rubberBand->hide();
        return;
    }

    QRect widgetRect = imageToWidget(m_selectionRect);
    m_rubberBand->setGeometry(widgetRect);
    m_rubberBand->show();
}

// Coordinate conversion methods
QPoint ImageCanvas::imageToWidget(const QPoint &imagePoint) const
{
    return QPoint(
        static_cast<int>(imagePoint.x() * m_zoomFactor + m_imageOffset.x()),
        static_cast<int>(imagePoint.y() * m_zoomFactor + m_imageOffset.y())
    );
}

QPoint ImageCanvas::widgetToImage(const QPoint &widgetPoint) const
{
    return QPoint(
        static_cast<int>((widgetPoint.x() - m_imageOffset.x()) / m_zoomFactor),
        static_cast<int>((widgetPoint.y() - m_imageOffset.y()) / m_zoomFactor)
    );
}

QRect ImageCanvas::imageToWidget(const QRect &imageRect) const
{
    QPoint topLeft = imageToWidget(imageRect.topLeft());
    QPoint bottomRight = imageToWidget(imageRect.bottomRight());
    return QRect(topLeft, bottomRight);
}

QRect ImageCanvas::widgetToImage(const QRect &widgetRect) const
{
    QPoint topLeft = widgetToImage(widgetRect.topLeft());
    QPoint bottomRight = widgetToImage(widgetRect.bottomRight());
    return QRect(topLeft, bottomRight);
}

// Image editing methods
void ImageCanvas::applyBrightnessContrast(int brightness, int contrast)
{
    if (m_image.isNull()) return;

    QImage image = m_image.toImage();

    // Apply brightness and contrast adjustments
    for (int y = 0; y < image.height(); ++y) {
        for (int x = 0; x < image.width(); ++x) {
            QRgb pixel = image.pixel(x, y);

            int r = qRed(pixel);
            int g = qGreen(pixel);
            int b = qBlue(pixel);

            // Apply brightness
            r = qBound(0, r + brightness, 255);
            g = qBound(0, g + brightness, 255);
            b = qBound(0, b + brightness, 255);

            // Apply contrast
            double contrastFactor = (259.0 * (contrast + 255.0)) / (255.0 * (259.0 - contrast));
            r = qBound(0, static_cast<int>(contrastFactor * (r - 128) + 128), 255);
            g = qBound(0, static_cast<int>(contrastFactor * (g - 128) + 128), 255);
            b = qBound(0, static_cast<int>(contrastFactor * (b - 128) + 128), 255);

            image.setPixel(x, y, qRgb(r, g, b));
        }
    }

    m_image = QPixmap::fromImage(image);
    update();
    emit imageChanged();
}

void ImageCanvas::applySaturation(int saturation)
{
    if (m_image.isNull()) return;

    QImage image = m_image.toImage();
    double satFactor = (saturation + 100.0) / 100.0;

    for (int y = 0; y < image.height(); ++y) {
        for (int x = 0; x < image.width(); ++x) {
            QRgb pixel = image.pixel(x, y);

            int r = qRed(pixel);
            int g = qGreen(pixel);
            int b = qBlue(pixel);

            // Convert to grayscale
            int gray = static_cast<int>(0.299 * r + 0.587 * g + 0.114 * b);

            // Apply saturation
            r = qBound(0, static_cast<int>(gray + satFactor * (r - gray)), 255);
            g = qBound(0, static_cast<int>(gray + satFactor * (g - gray)), 255);
            b = qBound(0, static_cast<int>(gray + satFactor * (b - gray)), 255);

            image.setPixel(x, y, qRgb(r, g, b));
        }
    }

    m_image = QPixmap::fromImage(image);
    update();
    emit imageChanged();
}

void ImageCanvas::applyBlur()
{
    applyImageFilter("blur");
}

void ImageCanvas::applySharpen()
{
    applyImageFilter("sharpen");
}

void ImageCanvas::applyImageFilter(const QString &filterType)
{
    if (m_image.isNull()) return;

    QImage image = m_image.toImage();
    QImage result = image;

    if (filterType == "blur") {
        // Simple box blur
        const int radius = 2;
        for (int y = radius; y < image.height() - radius; ++y) {
            for (int x = radius; x < image.width() - radius; ++x) {
                int r = 0, g = 0, b = 0, count = 0;

                for (int dy = -radius; dy <= radius; ++dy) {
                    for (int dx = -radius; dx <= radius; ++dx) {
                        QRgb pixel = image.pixel(x + dx, y + dy);
                        r += qRed(pixel);
                        g += qGreen(pixel);
                        b += qBlue(pixel);
                        count++;
                    }
                }

                result.setPixel(x, y, qRgb(r / count, g / count, b / count));
            }
        }
    } else if (filterType == "sharpen") {
        // Sharpen filter kernel
        int kernel[3][3] = {
            { 0, -1,  0},
            {-1,  5, -1},
            { 0, -1,  0}
        };

        for (int y = 1; y < image.height() - 1; ++y) {
            for (int x = 1; x < image.width() - 1; ++x) {
                int r = 0, g = 0, b = 0;

                for (int ky = 0; ky < 3; ++ky) {
                    for (int kx = 0; kx < 3; ++kx) {
                        QRgb pixel = image.pixel(x + kx - 1, y + ky - 1);
                        r += qRed(pixel) * kernel[ky][kx];
                        g += qGreen(pixel) * kernel[ky][kx];
                        b += qBlue(pixel) * kernel[ky][kx];
                    }
                }

                result.setPixel(x, y, qRgb(qBound(0, r, 255), qBound(0, g, 255), qBound(0, b, 255)));
            }
        }
    }

    m_image = QPixmap::fromImage(result);
    update();
    emit imageChanged();
}

void ImageCanvas::cropToSelection()
{
    if (!m_hasSelection || m_image.isNull()) return;

    m_image = m_image.copy(m_selectionRect);
    m_originalImage = m_image;

    // Reset view
    m_hasSelection = false;
    m_rubberBand->hide();
    updateImageRect();
    centerImage();
    update();

    emit imageChanged();
    emit selectionChanged();
}

void ImageCanvas::resizeImage(const QSize &newSize)
{
    if (m_image.isNull()) return;

    m_image = m_image.scaled(newSize, Qt::IgnoreAspectRatio, Qt::SmoothTransformation);
    m_originalImage = m_image;

    updateImageRect();
    centerImage();
    update();

    emit imageChanged();
}

void ImageCanvas::showResizeDialog()
{
    if (m_image.isNull()) return;

    QDialog dialog(this);
    dialog.setWindowTitle(tr("Resize Image"));
    dialog.setModal(true);

    QVBoxLayout *layout = new QVBoxLayout(&dialog);

    QGroupBox *sizeGroup = new QGroupBox(tr("New Size"));
    QGridLayout *sizeLayout = new QGridLayout(sizeGroup);

    QLabel *widthLabel = new QLabel(tr("Width:"));
    QSpinBox *widthSpinBox = new QSpinBox();
    widthSpinBox->setRange(1, 10000);
    widthSpinBox->setValue(m_image.width());

    QLabel *heightLabel = new QLabel(tr("Height:"));
    QSpinBox *heightSpinBox = new QSpinBox();
    heightSpinBox->setRange(1, 10000);
    heightSpinBox->setValue(m_image.height());

    QCheckBox *aspectRatioCheckBox = new QCheckBox(tr("Maintain aspect ratio"));
    aspectRatioCheckBox->setChecked(true);

    sizeLayout->addWidget(widthLabel, 0, 0);
    sizeLayout->addWidget(widthSpinBox, 0, 1);
    sizeLayout->addWidget(heightLabel, 1, 0);
    sizeLayout->addWidget(heightSpinBox, 1, 1);
    sizeLayout->addWidget(aspectRatioCheckBox, 2, 0, 1, 2);

    // Connect aspect ratio maintenance
    double aspectRatio = static_cast<double>(m_image.width()) / m_image.height();
    connect(widthSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), [=](int value) {
        if (aspectRatioCheckBox->isChecked()) {
            heightSpinBox->blockSignals(true);
            heightSpinBox->setValue(static_cast<int>(value / aspectRatio));
            heightSpinBox->blockSignals(false);
        }
    });

    connect(heightSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), [=](int value) {
        if (aspectRatioCheckBox->isChecked()) {
            widthSpinBox->blockSignals(true);
            widthSpinBox->setValue(static_cast<int>(value * aspectRatio));
            widthSpinBox->blockSignals(false);
        }
    });

    layout->addWidget(sizeGroup);

    // Buttons
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    QPushButton *okButton = new QPushButton(tr("OK"));
    QPushButton *cancelButton = new QPushButton(tr("Cancel"));

    connect(okButton, &QPushButton::clicked, &dialog, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, &dialog, &QDialog::reject);

    buttonLayout->addStretch();
    buttonLayout->addWidget(okButton);
    buttonLayout->addWidget(cancelButton);

    layout->addLayout(buttonLayout);

    if (dialog.exec() == QDialog::Accepted) {
        QSize newSize(widthSpinBox->value(), heightSpinBox->value());
        resizeImage(newSize);
    }
}

void ImageCanvas::showFiltersDialog()
{
    if (m_image.isNull()) return;

    QDialog dialog(this);
    dialog.setWindowTitle(tr("Apply Filters"));
    dialog.setModal(true);

    QVBoxLayout *layout = new QVBoxLayout(&dialog);

    QGroupBox *filtersGroup = new QGroupBox(tr("Available Filters"));
    QVBoxLayout *filtersLayout = new QVBoxLayout(filtersGroup);

    QPushButton *blurButton = new QPushButton(tr("Blur"));
    QPushButton *sharpenButton = new QPushButton(tr("Sharpen"));
    QPushButton *grayscaleButton = new QPushButton(tr("Grayscale"));

    connect(blurButton, &QPushButton::clicked, [this]() {
        applyBlur();
    });

    connect(sharpenButton, &QPushButton::clicked, [this]() {
        applySharpen();
    });

    connect(grayscaleButton, &QPushButton::clicked, [this]() {
        if (m_image.isNull()) return;
        QImage image = m_image.toImage();
        for (int y = 0; y < image.height(); ++y) {
            for (int x = 0; x < image.width(); ++x) {
                QRgb pixel = image.pixel(x, y);
                int gray = qGray(pixel);
                image.setPixel(x, y, qRgb(gray, gray, gray));
            }
        }
        m_image = QPixmap::fromImage(image);
        update();
        emit imageChanged();
    });

    filtersLayout->addWidget(blurButton);
    filtersLayout->addWidget(sharpenButton);
    filtersLayout->addWidget(grayscaleButton);

    layout->addWidget(filtersGroup);

    // Close button
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    QPushButton *closeButton = new QPushButton(tr("Close"));
    connect(closeButton, &QPushButton::clicked, &dialog, &QDialog::accept);

    buttonLayout->addStretch();
    buttonLayout->addWidget(closeButton);

    layout->addLayout(buttonLayout);

    dialog.exec();
}
