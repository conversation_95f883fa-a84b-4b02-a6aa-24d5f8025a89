#ifndef IMAGECANVAS_H
#define IMAGECANVAS_H

#include <QWidget>
#include <QPixmap>
#include <QPoint>
#include <QRect>
#include <QRubberBand>
#include <QUndoStack>

class QMouseEvent;
class QWheelEvent;
class QPaintEvent;
class QKeyEvent;

class ImageCanvas : public QWidget
{
    Q_OBJECT

public:
    enum Tool {
        SelectTool,
        CropTool,
        PanTool
    };

    explicit ImageCanvas(QWidget *parent = nullptr);
    ~ImageCanvas();

    // Image operations
    bool loadImage(const QString &fileName);
    bool saveImage(const QString &fileName);
    void newImage(int width = 800, int height = 600);
    bool hasImage() const { return !m_image.isNull(); }
    QSize getImageSize() const { return m_image.size(); }
    
    // Zoom operations
    void zoomIn();
    void zoomOut();
    void zoomFit();
    void zoomActual();
    void setZoomFactor(double factor);
    double getZoomFactor() const { return m_zoomFactor; }
    
    // View operations
    void resetView();
    void centerImage();
    
    // Selection operations
    void selectAll();
    void deselectAll();
    bool hasSelection() const { return m_hasSelection; }
    QRect getSelection() const { return m_selectionRect; }
    
    // Clipboard operations
    void cut();
    void copy();
    void paste();
    
    // Tool operations
    void setCurrentTool(Tool tool);
    Tool getCurrentTool() const { return m_currentTool; }
    void activateCropTool();
    
    // Image editing operations
    void applyBrightnessContrast(int brightness, int contrast);
    void applySaturation(int saturation);
    void applyBlur();
    void applySharpen();
    void cropToSelection();
    void resizeImage(const QSize &newSize);
    
    // Dialog operations
    void showResizeDialog();
    void showFiltersDialog();

signals:
    void imageChanged();
    void selectionChanged();
    void zoomChanged(double factor);
    void positionChanged(const QPoint &position);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void updateSelection();

private:
    void updateImageRect();
    void updateScrollBars();
    QPoint imageToWidget(const QPoint &imagePoint) const;
    QPoint widgetToImage(const QPoint &widgetPoint) const;
    QRect imageToWidget(const QRect &imageRect) const;
    QRect widgetToImage(const QRect &widgetRect) const;
    void drawGrid(QPainter &painter);
    void drawSelection(QPainter &painter);
    void drawImage(QPainter &painter);
    void applyImageFilter(const QString &filterType);
    
    // Image data
    QPixmap m_image;
    QPixmap m_originalImage;
    QRect m_imageRect;
    
    // View state
    double m_zoomFactor;
    QPoint m_imageOffset;
    QPoint m_lastPanPoint;
    bool m_isPanning;
    
    // Selection state
    bool m_hasSelection;
    QRect m_selectionRect;
    QRubberBand *m_rubberBand;
    QPoint m_selectionStart;
    bool m_isSelecting;
    
    // Tool state
    Tool m_currentTool;
    bool m_isDrawing;
    
    // Undo/Redo
    QUndoStack *m_undoStack;
    
    // Constants
    static const double MinZoomFactor;
    static const double MaxZoomFactor;
    static const double ZoomStep;
};

#endif // IMAGECANVAS_H
