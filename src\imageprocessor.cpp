#include "imageprocessor.h"
#include <QImage>
#include <QColor>
#include <QRect>
#include <QTransform>
#include <QtMath>
#include <QDebug>

ImageProcessor::ImageProcessor(QObject *parent)
    : QObject(parent)
{
}

ImageProcessor::~ImageProcessor()
{
}

// Color adjustment implementations
QImage ImageProcessor::adjustBrightness(const QImage &image, int brightness)
{
    QImage result = image;
    
    for (int y = 0; y < result.height(); ++y) {
        for (int x = 0; x < result.width(); ++x) {
            QRgb pixel = result.pixel(x, y);
            
            int r = qBound(0, qRed(pixel) + brightness, 255);
            int g = qBound(0, qGreen(pixel) + brightness, 255);
            int b = qBound(0, qBlue(pixel) + brightness, 255);
            
            result.setPixel(x, y, qRgb(r, g, b));
        }
    }
    
    return result;
}

QImage ImageProcessor::adjustContrast(const QImage &image, int contrast)
{
    QImage result = image;
    double contrastFactor = (259.0 * (contrast + 255.0)) / (255.0 * (259.0 - contrast));
    
    for (int y = 0; y < result.height(); ++y) {
        for (int x = 0; x < result.width(); ++x) {
            QRgb pixel = result.pixel(x, y);
            
            int r = qBound(0, static_cast<int>(contrastFactor * (qRed(pixel) - 128) + 128), 255);
            int g = qBound(0, static_cast<int>(contrastFactor * (qGreen(pixel) - 128) + 128), 255);
            int b = qBound(0, static_cast<int>(contrastFactor * (qBlue(pixel) - 128) + 128), 255);
            
            result.setPixel(x, y, qRgb(r, g, b));
        }
    }
    
    return result;
}

QImage ImageProcessor::adjustSaturation(const QImage &image, int saturation)
{
    QImage result = image;
    double satFactor = (saturation + 100.0) / 100.0;
    
    for (int y = 0; y < result.height(); ++y) {
        for (int x = 0; x < result.width(); ++x) {
            QRgb pixel = result.pixel(x, y);
            
            int r = qRed(pixel);
            int g = qGreen(pixel);
            int b = qBlue(pixel);
            
            // Convert to grayscale
            int gray = static_cast<int>(0.299 * r + 0.587 * g + 0.114 * b);
            
            // Apply saturation
            r = qBound(0, static_cast<int>(gray + satFactor * (r - gray)), 255);
            g = qBound(0, static_cast<int>(gray + satFactor * (g - gray)), 255);
            b = qBound(0, static_cast<int>(gray + satFactor * (b - gray)), 255);
            
            result.setPixel(x, y, qRgb(r, g, b));
        }
    }
    
    return result;
}

QImage ImageProcessor::adjustBrightnessContrast(const QImage &image, int brightness, int contrast)
{
    QImage result = adjustBrightness(image, brightness);
    return adjustContrast(result, contrast);
}

// Filter implementations
QImage ImageProcessor::applyBlur(const QImage &image, int radius)
{
    QImage result = image;
    
    for (int y = radius; y < image.height() - radius; ++y) {
        for (int x = radius; x < image.width() - radius; ++x) {
            int r = 0, g = 0, b = 0, count = 0;
            
            for (int dy = -radius; dy <= radius; ++dy) {
                for (int dx = -radius; dx <= radius; ++dx) {
                    QRgb pixel = image.pixel(x + dx, y + dy);
                    r += qRed(pixel);
                    g += qGreen(pixel);
                    b += qBlue(pixel);
                    count++;
                }
            }
            
            result.setPixel(x, y, qRgb(r / count, g / count, b / count));
        }
    }
    
    return result;
}

QImage ImageProcessor::applySharpen(const QImage &image)
{
    // Sharpen kernel
    QVector<QVector<double>> kernel = {
        { 0, -1,  0},
        {-1,  5, -1},
        { 0, -1,  0}
    };
    
    return applyConvolution(image, kernel);
}

QImage ImageProcessor::convertToGrayscale(const QImage &image)
{
    QImage result = image;
    
    for (int y = 0; y < result.height(); ++y) {
        for (int x = 0; x < result.width(); ++x) {
            QRgb pixel = result.pixel(x, y);
            int gray = qGray(pixel);
            result.setPixel(x, y, qRgb(gray, gray, gray));
        }
    }
    
    return result;
}

QImage ImageProcessor::convertToSepia(const QImage &image)
{
    QImage result = image;
    
    for (int y = 0; y < result.height(); ++y) {
        for (int x = 0; x < result.width(); ++x) {
            QRgb pixel = result.pixel(x, y);
            
            int r = qRed(pixel);
            int g = qGreen(pixel);
            int b = qBlue(pixel);
            
            int sepiaR = qBound(0, static_cast<int>(0.393 * r + 0.769 * g + 0.189 * b), 255);
            int sepiaG = qBound(0, static_cast<int>(0.349 * r + 0.686 * g + 0.168 * b), 255);
            int sepiaB = qBound(0, static_cast<int>(0.272 * r + 0.534 * g + 0.131 * b), 255);
            
            result.setPixel(x, y, qRgb(sepiaR, sepiaG, sepiaB));
        }
    }
    
    return result;
}

QImage ImageProcessor::invertColors(const QImage &image)
{
    QImage result = image;
    
    for (int y = 0; y < result.height(); ++y) {
        for (int x = 0; x < result.width(); ++x) {
            QRgb pixel = result.pixel(x, y);
            
            int r = 255 - qRed(pixel);
            int g = 255 - qGreen(pixel);
            int b = 255 - qBlue(pixel);
            
            result.setPixel(x, y, qRgb(r, g, b));
        }
    }
    
    return result;
}

// Geometric transformations
QImage ImageProcessor::rotateImage(const QImage &image, double angle)
{
    QTransform transform;
    transform.rotate(angle);
    return image.transformed(transform, Qt::SmoothTransformation);
}

QImage ImageProcessor::flipHorizontal(const QImage &image)
{
    return image.mirrored(true, false);
}

QImage ImageProcessor::flipVertical(const QImage &image)
{
    return image.mirrored(false, true);
}

QImage ImageProcessor::resizeImage(const QImage &image, const QSize &newSize, Qt::AspectRatioMode aspectRatio)
{
    return image.scaled(newSize, aspectRatio, Qt::SmoothTransformation);
}

QImage ImageProcessor::cropImage(const QImage &image, const QRect &cropRect)
{
    return image.copy(cropRect);
}

// Convolution implementation
QImage ImageProcessor::applyConvolution(const QImage &image, const QVector<QVector<double>> &kernel)
{
    QImage result = image;
    int kernelSize = kernel.size();
    int offset = kernelSize / 2;
    
    for (int y = offset; y < image.height() - offset; ++y) {
        for (int x = offset; x < image.width() - offset; ++x) {
            double r = 0, g = 0, b = 0;
            
            for (int ky = 0; ky < kernelSize; ++ky) {
                for (int kx = 0; kx < kernelSize; ++kx) {
                    QRgb pixel = image.pixel(x + kx - offset, y + ky - offset);
                    double weight = kernel[ky][kx];
                    
                    r += qRed(pixel) * weight;
                    g += qGreen(pixel) * weight;
                    b += qBlue(pixel) * weight;
                }
            }
            
            result.setPixel(x, y, qRgb(
                qBound(0, static_cast<int>(r), 255),
                qBound(0, static_cast<int>(g), 255),
                qBound(0, static_cast<int>(b), 255)
            ));
        }
    }
    
    return result;
}

// Utility functions
double ImageProcessor::calculateImageBrightness(const QImage &image)
{
    double totalBrightness = 0;
    int pixelCount = image.width() * image.height();
    
    for (int y = 0; y < image.height(); ++y) {
        for (int x = 0; x < image.width(); ++x) {
            QRgb pixel = image.pixel(x, y);
            totalBrightness += qGray(pixel);
        }
    }
    
    return totalBrightness / pixelCount;
}

QColor ImageProcessor::getAverageColor(const QImage &image)
{
    long long totalR = 0, totalG = 0, totalB = 0;
    int pixelCount = image.width() * image.height();
    
    for (int y = 0; y < image.height(); ++y) {
        for (int x = 0; x < image.width(); ++x) {
            QRgb pixel = image.pixel(x, y);
            totalR += qRed(pixel);
            totalG += qGreen(pixel);
            totalB += qBlue(pixel);
        }
    }
    
    return QColor(
        static_cast<int>(totalR / pixelCount),
        static_cast<int>(totalG / pixelCount),
        static_cast<int>(totalB / pixelCount)
    );
}

QColor ImageProcessor::getPixelColor(const QImage &image, const QPoint &position)
{
    if (position.x() >= 0 && position.x() < image.width() &&
        position.y() >= 0 && position.y() < image.height()) {
        return QColor(image.pixel(position));
    }
    return QColor();
}

// Helper functions
double ImageProcessor::clamp(double value, double min, double max)
{
    return qBound(min, value, max);
}

int ImageProcessor::clampInt(int value, int min, int max)
{
    return qBound(min, value, max);
}
