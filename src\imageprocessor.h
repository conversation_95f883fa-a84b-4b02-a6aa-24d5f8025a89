#ifndef IMAGEPROCESSOR_H
#define IMAGEPROCESSOR_H

#include <QObject>
#include <QPixmap>
#include <QImage>
#include <QColor>
#include <QRect>

class ImageProcessor : public QObject
{
    Q_OBJECT

public:
    explicit ImageProcessor(QObject *parent = nullptr);
    ~ImageProcessor();

    // Color adjustments
    static QImage adjustBrightness(const QImage &image, int brightness);
    static QImage adjustContrast(const QImage &image, int contrast);
    static QImage adjustSaturation(const QImage &image, int saturation);
    static QImage adjustBrightnessContrast(const QImage &image, int brightness, int contrast);
    static QImage adjustHue(const QImage &image, int hue);
    static QImage adjustGamma(const QImage &image, double gamma);
    
    // Filters
    static QImage applyBlur(const QImage &image, int radius = 2);
    static QImage applySharpen(const QImage &image);
    static QImage applyGaussianBlur(const QImage &image, double sigma);
    static QImage applyEdgeDetection(const QImage &image);
    static QImage applyEmboss(const QImage &image);
    
    // Color effects
    static QImage convertToGrayscale(const QImage &image);
    static QImage convertToSepia(const QImage &image);
    static QImage invertColors(const QImage &image);
    static QImage posterize(const QImage &image, int levels);
    
    // Geometric transformations
    static QImage rotateImage(const QImage &image, double angle);
    static QImage flipHorizontal(const QImage &image);
    static QImage flipVertical(const QImage &image);
    static QImage resizeImage(const QImage &image, const QSize &newSize, Qt::AspectRatioMode aspectRatio = Qt::IgnoreAspectRatio);
    static QImage cropImage(const QImage &image, const QRect &cropRect);
    
    // Noise and texture
    static QImage addNoise(const QImage &image, int intensity);
    static QImage removeNoise(const QImage &image);
    
    // Advanced filters
    static QImage applyConvolution(const QImage &image, const QVector<QVector<double>> &kernel);
    static QImage applyCustomFilter(const QImage &image, const QString &filterName);
    
    // Histogram operations
    static QVector<int> calculateHistogram(const QImage &image, int channel = -1); // -1 for luminance
    static QImage equalizeHistogram(const QImage &image);
    static QImage stretchHistogram(const QImage &image);
    
    // Color space conversions
    static QImage rgbToHsv(const QImage &image);
    static QImage hsvToRgb(const QImage &image);
    
    // Utility functions
    static double calculateImageBrightness(const QImage &image);
    static double calculateImageContrast(const QImage &image);
    static QColor getAverageColor(const QImage &image);
    static QColor getPixelColor(const QImage &image, const QPoint &position);
    
    // Batch processing
    static QList<QImage> processImageBatch(const QList<QImage> &images, 
                                          const QString &operation, 
                                          const QVariantMap &parameters);

signals:
    void processingStarted();
    void processingProgress(int percentage);
    void processingFinished();
    void processingError(const QString &error);

private:
    // Helper functions
    static QColor adjustColorBrightness(const QColor &color, int brightness);
    static QColor adjustColorContrast(const QColor &color, int contrast);
    static QColor adjustColorSaturation(const QColor &color, int saturation);
    static double clamp(double value, double min, double max);
    static int clampInt(int value, int min, int max);
    
    // Convolution kernels
    static QVector<QVector<double>> getBlurKernel(int radius);
    static QVector<QVector<double>> getSharpenKernel();
    static QVector<QVector<double>> getEdgeDetectionKernel();
    static QVector<QVector<double>> getEmbossKernel();
};

#endif // IMAGEPROCESSOR_H
