#include "layermanager.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QSlider>
#include <QCheckBox>
#include <QComboBox>
#include <QPushButton>
#include <QListWidget>
#include <QListWidgetItem>
#include <QGroupBox>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QPainter>
#include <QInputDialog>
#include <QMessageBox>

// Layer class implementation
Layer::Layer(const QString &name, const QPixmap &pixmap)
    : m_name(name)
    , m_pixmap(pixmap)
    , m_visible(true)
    , m_opacity(100)
    , m_blendMode("Normal")
{
}

// LayerListWidget implementation
LayerListWidget::LayerListWidget(QWidget *parent)
    : QListWidget(parent)
{
    setDragDropMode(QAbstractItemView::InternalMove);
    setDefaultDropAction(Qt::MoveAction);
}

void LayerListWidget::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasFormat("application/x-qabstractitemmodeldatalist")) {
        event->acceptProposedAction();
    } else {
        QListWidget::dragEnterEvent(event);
    }
}

void LayerListWidget::dragMoveEvent(QDragMoveEvent *event)
{
    if (event->mimeData()->hasFormat("application/x-qabstractitemmodeldatalist")) {
        event->acceptProposedAction();
    } else {
        QListWidget::dragMoveEvent(event);
    }
}

void LayerListWidget::dropEvent(QDropEvent *event)
{
    QListWidget::dropEvent(event);
    emit layersReordered();
}

// LayerManager implementation
LayerManager::LayerManager(QWidget *parent)
    : QWidget(parent)
    , m_layerList(nullptr)
    , m_addButton(nullptr)
    , m_removeButton(nullptr)
    , m_duplicateButton(nullptr)
    , m_moveUpButton(nullptr)
    , m_moveDownButton(nullptr)
    , m_visibilityCheckBox(nullptr)
    , m_opacitySlider(nullptr)
    , m_opacityLabel(nullptr)
    , m_blendModeCombo(nullptr)
    , m_currentLayerIndex(-1)
{
    setupUI();
    connectSignals();
    
    // Add default layer
    addLayer("Background", QPixmap(800, 600));
}

LayerManager::~LayerManager()
{
    qDeleteAll(m_layers);
}

void LayerManager::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    
    // Layer list
    m_layerList = new LayerListWidget();
    m_layerList->setMinimumHeight(200);
    mainLayout->addWidget(m_layerList);
    
    // Layer buttons
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    
    m_addButton = new QPushButton("+");
    m_addButton->setToolTip(tr("Add new layer"));
    m_addButton->setMaximumWidth(30);
    
    m_removeButton = new QPushButton("-");
    m_removeButton->setToolTip(tr("Remove current layer"));
    m_removeButton->setMaximumWidth(30);
    
    m_duplicateButton = new QPushButton("D");
    m_duplicateButton->setToolTip(tr("Duplicate current layer"));
    m_duplicateButton->setMaximumWidth(30);
    
    m_moveUpButton = new QPushButton("↑");
    m_moveUpButton->setToolTip(tr("Move layer up"));
    m_moveUpButton->setMaximumWidth(30);
    
    m_moveDownButton = new QPushButton("↓");
    m_moveDownButton->setToolTip(tr("Move layer down"));
    m_moveDownButton->setMaximumWidth(30);
    
    buttonLayout->addWidget(m_addButton);
    buttonLayout->addWidget(m_removeButton);
    buttonLayout->addWidget(m_duplicateButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_moveUpButton);
    buttonLayout->addWidget(m_moveDownButton);
    
    mainLayout->addLayout(buttonLayout);
    
    // Layer properties
    QGroupBox *propertiesGroup = new QGroupBox(tr("Layer Properties"));
    QVBoxLayout *propsLayout = new QVBoxLayout(propertiesGroup);
    
    // Visibility
    m_visibilityCheckBox = new QCheckBox(tr("Visible"));
    m_visibilityCheckBox->setChecked(true);
    propsLayout->addWidget(m_visibilityCheckBox);
    
    // Opacity
    QHBoxLayout *opacityLayout = new QHBoxLayout();
    opacityLayout->addWidget(new QLabel(tr("Opacity:")));
    
    m_opacitySlider = new QSlider(Qt::Horizontal);
    m_opacitySlider->setRange(0, 100);
    m_opacitySlider->setValue(100);
    
    m_opacityLabel = new QLabel("100%");
    m_opacityLabel->setMinimumWidth(40);
    
    opacityLayout->addWidget(m_opacitySlider);
    opacityLayout->addWidget(m_opacityLabel);
    propsLayout->addLayout(opacityLayout);
    
    // Blend mode
    QHBoxLayout *blendLayout = new QHBoxLayout();
    blendLayout->addWidget(new QLabel(tr("Blend:")));
    
    m_blendModeCombo = new QComboBox();
    m_blendModeCombo->addItems({
        "Normal", "Multiply", "Screen", "Overlay", 
        "Soft Light", "Hard Light", "Color Dodge", "Color Burn"
    });
    
    blendLayout->addWidget(m_blendModeCombo);
    propsLayout->addLayout(blendLayout);
    
    mainLayout->addWidget(propertiesGroup);
    mainLayout->addStretch();
}

void LayerManager::connectSignals()
{
    // Layer list signals
    connect(m_layerList, &QListWidget::currentRowChanged, this, &LayerManager::onCurrentLayerChanged);
    connect(m_layerList, &LayerListWidget::layersReordered, this, &LayerManager::layerChanged);
    
    // Button signals
    connect(m_addButton, &QPushButton::clicked, this, &LayerManager::addNewLayer);
    connect(m_removeButton, &QPushButton::clicked, this, &LayerManager::removeCurrentLayer);
    connect(m_duplicateButton, &QPushButton::clicked, this, &LayerManager::duplicateCurrentLayer);
    connect(m_moveUpButton, &QPushButton::clicked, this, &LayerManager::moveCurrentLayerUp);
    connect(m_moveDownButton, &QPushButton::clicked, this, &LayerManager::moveCurrentLayerDown);
    
    // Property signals
    connect(m_visibilityCheckBox, &QCheckBox::toggled, this, &LayerManager::onLayerVisibilityChanged);
    connect(m_opacitySlider, &QSlider::valueChanged, this, &LayerManager::onLayerOpacityChanged);
    connect(m_blendModeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), 
            this, &LayerManager::onLayerBlendModeChanged);
}

void LayerManager::addLayer(const QString &name, const QPixmap &pixmap)
{
    Layer *layer = new Layer(name, pixmap);
    m_layers.append(layer);
    
    updateLayerList();
    
    // Select the new layer
    m_layerList->setCurrentRow(m_layers.size() - 1);
    
    emit layerChanged();
}

void LayerManager::removeLayer(int index)
{
    if (index >= 0 && index < m_layers.size()) {
        delete m_layers.takeAt(index);
        updateLayerList();
        
        // Adjust current selection
        if (m_currentLayerIndex >= m_layers.size()) {
            m_currentLayerIndex = m_layers.size() - 1;
        }
        
        if (m_currentLayerIndex >= 0) {
            m_layerList->setCurrentRow(m_currentLayerIndex);
        }
        
        emit layerChanged();
    }
}

void LayerManager::duplicateLayer(int index)
{
    if (index >= 0 && index < m_layers.size()) {
        Layer *original = m_layers[index];
        QString newName = original->name() + " Copy";
        
        Layer *duplicate = new Layer(newName, original->pixmap());
        duplicate->setVisible(original->isVisible());
        duplicate->setOpacity(original->opacity());
        duplicate->setBlendMode(original->blendMode());
        
        m_layers.insert(index + 1, duplicate);
        updateLayerList();
        
        // Select the duplicated layer
        m_layerList->setCurrentRow(index + 1);
        
        emit layerChanged();
    }
}

void LayerManager::moveLayerUp(int index)
{
    if (index > 0 && index < m_layers.size()) {
        m_layers.swapItemsAt(index, index - 1);
        updateLayerList();
        m_layerList->setCurrentRow(index - 1);
        emit layerChanged();
    }
}

void LayerManager::moveLayerDown(int index)
{
    if (index >= 0 && index < m_layers.size() - 1) {
        m_layers.swapItemsAt(index, index + 1);
        updateLayerList();
        m_layerList->setCurrentRow(index + 1);
        emit layerChanged();
    }
}

void LayerManager::setLayerVisible(int index, bool visible)
{
    if (index >= 0 && index < m_layers.size()) {
        m_layers[index]->setVisible(visible);
        updateLayerList();
        emit layerVisibilityChanged(index, visible);
        emit layerChanged();
    }
}

void LayerManager::setLayerOpacity(int index, int opacity)
{
    if (index >= 0 && index < m_layers.size()) {
        m_layers[index]->setOpacity(opacity);
        emit layerOpacityChanged(index, opacity);
        emit layerChanged();
    }
}

void LayerManager::setLayerBlendMode(int index, const QString &mode)
{
    if (index >= 0 && index < m_layers.size()) {
        m_layers[index]->setBlendMode(mode);
        emit layerChanged();
    }
}

void LayerManager::setLayerName(int index, const QString &name)
{
    if (index >= 0 && index < m_layers.size()) {
        m_layers[index]->setName(name);
        updateLayerList();
        emit layerChanged();
    }
}

Layer* LayerManager::getLayer(int index) const
{
    if (index >= 0 && index < m_layers.size()) {
        return m_layers[index];
    }
    return nullptr;
}

int LayerManager::currentLayerIndex() const
{
    return m_currentLayerIndex;
}

Layer* LayerManager::currentLayer() const
{
    return getLayer(m_currentLayerIndex);
}

QPixmap LayerManager::getCompositeImage() const
{
    if (m_layers.isEmpty()) {
        return QPixmap();
    }

    // Find the maximum size needed
    QSize maxSize;
    for (const Layer *layer : m_layers) {
        if (layer->isVisible() && !layer->pixmap().isNull()) {
            QSize layerSize = layer->pixmap().size();
            maxSize = maxSize.expandedTo(layerSize);
        }
    }

    if (maxSize.isEmpty()) {
        return QPixmap();
    }

    // Create composite image
    QPixmap composite(maxSize);
    composite.fill(Qt::transparent);

    QPainter painter(&composite);
    painter.setCompositionMode(QPainter::CompositionMode_SourceOver);

    // Composite layers from bottom to top
    for (int i = 0; i < m_layers.size(); ++i) {
        const Layer *layer = m_layers[i];
        if (layer->isVisible() && !layer->pixmap().isNull()) {
            // Apply opacity
            painter.setOpacity(layer->opacity() / 100.0);

            // Simple blend mode support (just normal for now)
            if (layer->blendMode() == "Multiply") {
                painter.setCompositionMode(QPainter::CompositionMode_Multiply);
            } else if (layer->blendMode() == "Screen") {
                painter.setCompositionMode(QPainter::CompositionMode_Screen);
            } else if (layer->blendMode() == "Overlay") {
                painter.setCompositionMode(QPainter::CompositionMode_Overlay);
            } else {
                painter.setCompositionMode(QPainter::CompositionMode_SourceOver);
            }

            painter.drawPixmap(0, 0, layer->pixmap());
        }
    }

    return composite;
}

// Slot implementations
void LayerManager::onCurrentLayerChanged()
{
    m_currentLayerIndex = m_layerList->currentRow();
    updateLayerControls();
    emit currentLayerChanged(m_currentLayerIndex);
}

void LayerManager::onLayerVisibilityChanged()
{
    if (m_currentLayerIndex >= 0 && m_currentLayerIndex < m_layers.size()) {
        bool visible = m_visibilityCheckBox->isChecked();
        setLayerVisible(m_currentLayerIndex, visible);
    }
}

void LayerManager::onLayerOpacityChanged()
{
    if (m_currentLayerIndex >= 0 && m_currentLayerIndex < m_layers.size()) {
        int opacity = m_opacitySlider->value();
        m_opacityLabel->setText(QString("%1%").arg(opacity));
        setLayerOpacity(m_currentLayerIndex, opacity);
    }
}

void LayerManager::onLayerBlendModeChanged()
{
    if (m_currentLayerIndex >= 0 && m_currentLayerIndex < m_layers.size()) {
        QString mode = m_blendModeCombo->currentText();
        setLayerBlendMode(m_currentLayerIndex, mode);
    }
}

void LayerManager::addNewLayer()
{
    bool ok;
    QString name = QInputDialog::getText(this, tr("New Layer"),
                                       tr("Layer name:"), QLineEdit::Normal,
                                       tr("Layer %1").arg(m_layers.size() + 1), &ok);
    if (ok && !name.isEmpty()) {
        // Create a transparent layer
        QPixmap pixmap(800, 600);
        pixmap.fill(Qt::transparent);
        addLayer(name, pixmap);
    }
}

void LayerManager::removeCurrentLayer()
{
    if (m_layers.size() <= 1) {
        QMessageBox::information(this, tr("Layer Manager"),
                               tr("Cannot remove the last layer."));
        return;
    }

    if (m_currentLayerIndex >= 0) {
        int ret = QMessageBox::question(this, tr("Remove Layer"),
                                      tr("Are you sure you want to remove this layer?"),
                                      QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::Yes) {
            removeLayer(m_currentLayerIndex);
        }
    }
}

void LayerManager::duplicateCurrentLayer()
{
    if (m_currentLayerIndex >= 0) {
        duplicateLayer(m_currentLayerIndex);
    }
}

void LayerManager::moveCurrentLayerUp()
{
    if (m_currentLayerIndex >= 0) {
        moveLayerUp(m_currentLayerIndex);
    }
}

void LayerManager::moveCurrentLayerDown()
{
    if (m_currentLayerIndex >= 0) {
        moveLayerDown(m_currentLayerIndex);
    }
}

void LayerManager::updateLayerList()
{
    m_layerList->clear();

    // Add layers in reverse order (top layer first in list)
    for (int i = m_layers.size() - 1; i >= 0; --i) {
        const Layer *layer = m_layers[i];
        QListWidgetItem *item = new QListWidgetItem();

        QString displayText = layer->name();
        if (!layer->isVisible()) {
            displayText += " (Hidden)";
        }
        if (layer->opacity() < 100) {
            displayText += QString(" (%1%)").arg(layer->opacity());
        }

        item->setText(displayText);
        item->setData(Qt::UserRole, i); // Store actual layer index

        m_layerList->addItem(item);
    }
}

void LayerManager::updateLayerControls()
{
    bool hasLayer = (m_currentLayerIndex >= 0 && m_currentLayerIndex < m_layers.size());

    // Enable/disable controls
    m_removeButton->setEnabled(hasLayer && m_layers.size() > 1);
    m_duplicateButton->setEnabled(hasLayer);
    m_moveUpButton->setEnabled(hasLayer && m_currentLayerIndex > 0);
    m_moveDownButton->setEnabled(hasLayer && m_currentLayerIndex < m_layers.size() - 1);

    m_visibilityCheckBox->setEnabled(hasLayer);
    m_opacitySlider->setEnabled(hasLayer);
    m_blendModeCombo->setEnabled(hasLayer);

    if (hasLayer) {
        const Layer *layer = m_layers[m_currentLayerIndex];

        // Block signals to prevent recursive updates
        m_visibilityCheckBox->blockSignals(true);
        m_opacitySlider->blockSignals(true);
        m_blendModeCombo->blockSignals(true);

        m_visibilityCheckBox->setChecked(layer->isVisible());
        m_opacitySlider->setValue(layer->opacity());
        m_opacityLabel->setText(QString("%1%").arg(layer->opacity()));

        int blendIndex = m_blendModeCombo->findText(layer->blendMode());
        if (blendIndex >= 0) {
            m_blendModeCombo->setCurrentIndex(blendIndex);
        }

        // Restore signals
        m_visibilityCheckBox->blockSignals(false);
        m_opacitySlider->blockSignals(false);
        m_blendModeCombo->blockSignals(false);
    }
}

#include "layermanager.moc"
