#ifndef LAYERMANAGER_H
#define LAYERMANAGER_H

#include <QWidget>
#include <QListWidget>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QSlider>
#include <QCheckBox>
#include <QPixmap>

class Layer
{
public:
    Layer(const QString &name, const QPixmap &pixmap = QPixmap());
    
    QString name() const { return m_name; }
    void setName(const QString &name) { m_name = name; }
    
    QPixmap pixmap() const { return m_pixmap; }
    void setPixmap(const QPixmap &pixmap) { m_pixmap = pixmap; }
    
    bool isVisible() const { return m_visible; }
    void setVisible(bool visible) { m_visible = visible; }
    
    int opacity() const { return m_opacity; }
    void setOpacity(int opacity) { m_opacity = qBound(0, opacity, 100); }
    
    QString blendMode() const { return m_blendMode; }
    void setBlendMode(const QString &mode) { m_blendMode = mode; }

private:
    QString m_name;
    QPixmap m_pixmap;
    bool m_visible;
    int m_opacity;
    QString m_blendMode;
};

class LayerListWidget : public QListWidget
{
    Q_OBJECT

public:
    explicit LayerListWidget(QWidget *parent = nullptr);

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dragMoveEvent(QDragMoveEvent *event) override;
    void dropEvent(QDropEvent *event) override;

signals:
    void layersReordered();
};

class LayerManager : public QWidget
{
    Q_OBJECT

public:
    explicit LayerManager(QWidget *parent = nullptr);
    ~LayerManager();

    // Layer management
    void addLayer(const QString &name, const QPixmap &pixmap = QPixmap());
    void removeLayer(int index);
    void duplicateLayer(int index);
    void moveLayerUp(int index);
    void moveLayerDown(int index);
    
    // Layer properties
    void setLayerVisible(int index, bool visible);
    void setLayerOpacity(int index, int opacity);
    void setLayerBlendMode(int index, const QString &mode);
    void setLayerName(int index, const QString &name);
    
    // Access
    int layerCount() const { return m_layers.size(); }
    Layer* getLayer(int index) const;
    int currentLayerIndex() const;
    Layer* currentLayer() const;
    int layerCount() const { return m_layers.size(); }
    
    // Composite image
    QPixmap getCompositeImage() const;

public slots:
    void onCurrentLayerChanged();
    void onLayerVisibilityChanged();
    void onLayerOpacityChanged();
    void onLayerBlendModeChanged();

signals:
    void layerChanged();
    void currentLayerChanged(int index);
    void layerVisibilityChanged(int index, bool visible);
    void layerOpacityChanged(int index, int opacity);

private slots:
    void addNewLayer();
    void removeCurrentLayer();
    void duplicateCurrentLayer();
    void moveCurrentLayerUp();
    void moveCurrentLayerDown();
    void updateLayerProperties();

private:
    void setupUI();
    void updateLayerList();
    void updateLayerControls();
    void connectSignals();
    
    // UI components
    LayerListWidget *m_layerList;
    QPushButton *m_addButton;
    QPushButton *m_removeButton;
    QPushButton *m_duplicateButton;
    QPushButton *m_moveUpButton;
    QPushButton *m_moveDownButton;
    
    // Layer properties controls
    QCheckBox *m_visibilityCheckBox;
    QSlider *m_opacitySlider;
    QLabel *m_opacityLabel;
    QComboBox *m_blendModeCombo;
    
    // Data
    QList<Layer*> m_layers;
    int m_currentLayerIndex;
};

#endif // LAYERMANAGER_H
