#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include "mainwindow.h"

void setDarkTheme(QApplication &app) {
    // Set dark palette
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
    app.setPalette(darkPalette);
    
    // Set dark style sheet for modern look with rounded corners
    QString styleSheet = R"(
        QMainWindow {
            background-color: #2b2b2b;
            color: white;
        }
        
        QMenuBar {
            background-color: #353535;
            color: white;
            border: none;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        QMenuBar::item:selected {
            background-color: #2a82da;
        }
        
        QMenu {
            background-color: #353535;
            color: white;
            border: 1px solid #555;
            border-radius: 6px;
        }
        
        QMenu::item {
            padding: 6px 20px;
            border-radius: 4px;
        }
        
        QMenu::item:selected {
            background-color: #2a82da;
        }
        
        QToolBar {
            background-color: #353535;
            border: none;
            spacing: 2px;
        }
        
        QToolButton {
            background-color: #454545;
            border: none;
            border-radius: 6px;
            padding: 6px;
            margin: 2px;
        }
        
        QToolButton:hover {
            background-color: #555555;
        }
        
        QToolButton:pressed {
            background-color: #2a82da;
        }
        
        QPushButton {
            background-color: #454545;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #555555;
        }
        
        QPushButton:pressed {
            background-color: #2a82da;
        }
        
        QScrollArea {
            background-color: #2b2b2b;
            border: 1px solid #555;
            border-radius: 6px;
        }
        
        QScrollBar:vertical {
            background-color: #353535;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #555555;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #666666;
        }
        
        QDockWidget {
            background-color: #353535;
            color: white;
            border: 1px solid #555;
            border-radius: 6px;
        }
        
        QDockWidget::title {
            background-color: #454545;
            padding: 6px;
            border-radius: 4px;
        }
        
        QStatusBar {
            background-color: #353535;
            color: white;
            border-top: 1px solid #555;
        }
    )";
    
    app.setStyleSheet(styleSheet);
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set application properties
    app.setApplicationName("PhotoEditor");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("PhotoEditor Team");
    
    // Apply dark theme
    setDarkTheme(app);
    
    // Create and show main window
    MainWindow window;
    window.show();
    
    return app.exec();
}
