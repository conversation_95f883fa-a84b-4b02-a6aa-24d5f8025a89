#include "mainwindow.h"
#include "imagecanvas.h"
#include "layermanager.h"
#include "toolpalette.h"
#include "projectmanager.h"

#include <QApplication>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QDockWidget>
#include <QScrollArea>
#include <QLabel>
#include <QSlider>
#include <QSpinBox>
#include <QUndoStack>
#include <QUndoView>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QComboBox>
#include <QCheckBox>
#include <QProgressBar>
#include <QAction>
#include <QActionGroup>
#include <QFileDialog>
#include <QMessageBox>
#include <QSettings>
#include <QCloseEvent>
#include <QKeySequence>
#include <QDesktopServices>
#include <QUrl>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainSplitter(nullptr)
    , m_scrollArea(nullptr)
    , m_imageCanvas(nullptr)
    , m_layerDock(nullptr)
    , m_toolDock(nullptr)
    , m_historyDock(nullptr)
    , m_propertiesDock(nullptr)
    , m_layerManager(nullptr)
    , m_toolPalette(nullptr)
    , m_undoView(nullptr)
    , m_fileMenu(nullptr)
    , m_editMenu(nullptr)
    , m_viewMenu(nullptr)
    , m_toolsMenu(nullptr)
    , m_helpMenu(nullptr)
    , m_recentFilesMenu(nullptr)
    , m_fileToolBar(nullptr)
    , m_editToolBar(nullptr)
    , m_viewToolBar(nullptr)
    , m_toolsToolBar(nullptr)
    , m_statusLabel(nullptr)
    , m_zoomLabel(nullptr)
    , m_positionLabel(nullptr)
    , m_sizeLabel(nullptr)
    , m_progressBar(nullptr)
    , m_undoStack(nullptr)
    , m_projectManager(nullptr)
    , m_imagePropsGroup(nullptr)
    , m_colorAdjustGroup(nullptr)
    , m_brightnessSlider(nullptr)
    , m_contrastSlider(nullptr)
    , m_saturationSlider(nullptr)
    , m_brightnessSpinBox(nullptr)
    , m_contrastSpinBox(nullptr)
    , m_saturationSpinBox(nullptr)
    , m_isModified(false)
    , m_zoomFactor(1.0)
{
    setWindowTitle("PhotoEditor - Untitled");
    setMinimumSize(800, 600);
    resize(1200, 800);
    
    // Initialize core components
    m_undoStack = new QUndoStack(this);
    m_projectManager = new ProjectManager(this);
    
    // Create UI components
    createActions();
    createMenus();
    createToolBars();
    createStatusBar();
    setupCentralWidget();
    createDockWidgets();
    connectSignals();
    
    // Load settings
    loadSettings();
    
    // Update UI state
    updateStatusBar();
    updateZoomLabel();
}

MainWindow::~MainWindow()
{
    saveSettings();
}

void MainWindow::createActions()
{
    // File actions
    m_newAction = new QAction(QIcon(":/icons/new.png"), tr("&New Project"), this);
    m_newAction->setShortcuts(QKeySequence::New);
    m_newAction->setStatusTip(tr("Create a new project"));
    connect(m_newAction, &QAction::triggered, this, &MainWindow::newProject);
    
    m_openAction = new QAction(QIcon(":/icons/open.png"), tr("&Open Project"), this);
    m_openAction->setShortcuts(QKeySequence::Open);
    m_openAction->setStatusTip(tr("Open an existing project"));
    connect(m_openAction, &QAction::triggered, this, &MainWindow::openProject);
    
    m_saveAction = new QAction(QIcon(":/icons/save.png"), tr("&Save Project"), this);
    m_saveAction->setShortcuts(QKeySequence::Save);
    m_saveAction->setStatusTip(tr("Save the current project"));
    connect(m_saveAction, &QAction::triggered, this, &MainWindow::saveProject);
    
    m_saveAsAction = new QAction(tr("Save Project &As..."), this);
    m_saveAsAction->setShortcuts(QKeySequence::SaveAs);
    m_saveAsAction->setStatusTip(tr("Save the project with a new name"));
    connect(m_saveAsAction, &QAction::triggered, this, &MainWindow::saveProjectAs);
    
    m_importAction = new QAction(QIcon(":/icons/import.png"), tr("&Import Image"), this);
    m_importAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_I));
    m_importAction->setStatusTip(tr("Import an image file"));
    connect(m_importAction, &QAction::triggered, this, &MainWindow::importImage);
    
    m_exportAction = new QAction(QIcon(":/icons/export.png"), tr("&Export Image"), this);
    m_exportAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_E));
    m_exportAction->setStatusTip(tr("Export the current image"));
    connect(m_exportAction, &QAction::triggered, this, &MainWindow::exportImage);
    
    m_exitAction = new QAction(tr("E&xit"), this);
    m_exitAction->setShortcuts(QKeySequence::Quit);
    m_exitAction->setStatusTip(tr("Exit the application"));
    connect(m_exitAction, &QAction::triggered, this, &MainWindow::exitApplication);
    
    // Edit actions
    m_undoAction = m_undoStack->createUndoAction(this, tr("&Undo"));
    m_undoAction->setIcon(QIcon(":/icons/undo.png"));
    m_undoAction->setShortcuts(QKeySequence::Undo);
    
    m_redoAction = m_undoStack->createRedoAction(this, tr("&Redo"));
    m_redoAction->setIcon(QIcon(":/icons/redo.png"));
    m_redoAction->setShortcuts(QKeySequence::Redo);
    
    m_cutAction = new QAction(QIcon(":/icons/cut.png"), tr("Cu&t"), this);
    m_cutAction->setShortcuts(QKeySequence::Cut);
    m_cutAction->setStatusTip(tr("Cut the selection"));
    connect(m_cutAction, &QAction::triggered, this, &MainWindow::cut);
    
    m_copyAction = new QAction(QIcon(":/icons/copy.png"), tr("&Copy"), this);
    m_copyAction->setShortcuts(QKeySequence::Copy);
    m_copyAction->setStatusTip(tr("Copy the selection"));
    connect(m_copyAction, &QAction::triggered, this, &MainWindow::copy);
    
    m_pasteAction = new QAction(QIcon(":/icons/paste.png"), tr("&Paste"), this);
    m_pasteAction->setShortcuts(QKeySequence::Paste);
    m_pasteAction->setStatusTip(tr("Paste from clipboard"));
    connect(m_pasteAction, &QAction::triggered, this, &MainWindow::paste);
    
    m_selectAllAction = new QAction(tr("Select &All"), this);
    m_selectAllAction->setShortcuts(QKeySequence::SelectAll);
    m_selectAllAction->setStatusTip(tr("Select all"));
    connect(m_selectAllAction, &QAction::triggered, this, &MainWindow::selectAll);
    
    m_deselectAllAction = new QAction(tr("&Deselect All"), this);
    m_deselectAllAction->setShortcut(QKeySequence(Qt::CTRL | Qt::SHIFT | Qt::Key_A));
    m_deselectAllAction->setStatusTip(tr("Deselect all"));
    connect(m_deselectAllAction, &QAction::triggered, this, &MainWindow::deselectAll);

    // View actions
    m_zoomInAction = new QAction(QIcon(":/icons/zoom_in.png"), tr("Zoom &In"), this);
    m_zoomInAction->setShortcut(QKeySequence::ZoomIn);
    m_zoomInAction->setStatusTip(tr("Zoom in"));
    connect(m_zoomInAction, &QAction::triggered, this, &MainWindow::zoomIn);

    m_zoomOutAction = new QAction(QIcon(":/icons/zoom_out.png"), tr("Zoom &Out"), this);
    m_zoomOutAction->setShortcut(QKeySequence::ZoomOut);
    m_zoomOutAction->setStatusTip(tr("Zoom out"));
    connect(m_zoomOutAction, &QAction::triggered, this, &MainWindow::zoomOut);

    m_zoomFitAction = new QAction(QIcon(":/icons/zoom_fit.png"), tr("&Fit to Window"), this);
    m_zoomFitAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_0));
    m_zoomFitAction->setStatusTip(tr("Fit image to window"));
    connect(m_zoomFitAction, &QAction::triggered, this, &MainWindow::zoomFit);

    m_zoomActualAction = new QAction(QIcon(":/icons/zoom_actual.png"), tr("&Actual Size"), this);
    m_zoomActualAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_1));
    m_zoomActualAction->setStatusTip(tr("Show actual size"));
    connect(m_zoomActualAction, &QAction::triggered, this, &MainWindow::zoomActual);

    m_fullScreenAction = new QAction(tr("&Full Screen"), this);
    m_fullScreenAction->setShortcut(QKeySequence::FullScreen);
    m_fullScreenAction->setStatusTip(tr("Toggle full screen mode"));
    m_fullScreenAction->setCheckable(true);
    connect(m_fullScreenAction, &QAction::triggered, this, &MainWindow::toggleFullScreen);

    m_resetViewAction = new QAction(tr("&Reset View"), this);
    m_resetViewAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_R));
    m_resetViewAction->setStatusTip(tr("Reset view to default"));
    connect(m_resetViewAction, &QAction::triggered, this, &MainWindow::resetView);

    // Tools actions
    m_cropAction = new QAction(QIcon(":/icons/crop.png"), tr("&Crop"), this);
    m_cropAction->setShortcut(QKeySequence(Qt::Key_C));
    m_cropAction->setStatusTip(tr("Crop the image"));
    connect(m_cropAction, &QAction::triggered, this, &MainWindow::cropTool);

    m_resizeAction = new QAction(QIcon(":/icons/resize.png"), tr("&Resize"), this);
    m_resizeAction->setShortcut(QKeySequence(Qt::CTRL | Qt::SHIFT | Qt::Key_R));
    m_resizeAction->setStatusTip(tr("Resize the image"));
    connect(m_resizeAction, &QAction::triggered, this, &MainWindow::resizeTool);

    m_colorAdjustAction = new QAction(QIcon(":/icons/color.png"), tr("Color &Adjustments"), this);
    m_colorAdjustAction->setShortcut(QKeySequence(Qt::CTRL | Qt::Key_L));
    m_colorAdjustAction->setStatusTip(tr("Adjust colors"));
    connect(m_colorAdjustAction, &QAction::triggered, this, &MainWindow::colorAdjustments);

    m_blurAction = new QAction(QIcon(":/icons/blur.png"), tr("&Blur"), this);
    m_blurAction->setStatusTip(tr("Apply blur filter"));
    connect(m_blurAction, &QAction::triggered, this, &MainWindow::applyBlur);

    m_sharpenAction = new QAction(QIcon(":/icons/sharpen.png"), tr("&Sharpen"), this);
    m_sharpenAction->setStatusTip(tr("Apply sharpen filter"));
    connect(m_sharpenAction, &QAction::triggered, this, &MainWindow::applySharpen);

    m_filtersAction = new QAction(QIcon(":/icons/filters.png"), tr("&Filters"), this);
    m_filtersAction->setStatusTip(tr("Apply various filters"));
    connect(m_filtersAction, &QAction::triggered, this, &MainWindow::applyFilters);

    // Help actions
    m_aboutAction = new QAction(tr("&About"), this);
    m_aboutAction->setStatusTip(tr("Show the application's About box"));
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::showAbout);

    m_helpAction = new QAction(tr("&Help"), this);
    m_helpAction->setShortcut(QKeySequence::HelpContents);
    m_helpAction->setStatusTip(tr("Show help"));
    connect(m_helpAction, &QAction::triggered, this, &MainWindow::showHelp);
}

void MainWindow::createMenus()
{
    // File menu
    m_fileMenu = menuBar()->addMenu(tr("&File"));
    m_fileMenu->addAction(m_newAction);
    m_fileMenu->addAction(m_openAction);
    m_fileMenu->addSeparator();
    m_fileMenu->addAction(m_saveAction);
    m_fileMenu->addAction(m_saveAsAction);
    m_fileMenu->addSeparator();
    m_fileMenu->addAction(m_importAction);
    m_fileMenu->addAction(m_exportAction);
    m_fileMenu->addSeparator();

    // Recent files submenu
    m_recentFilesMenu = m_fileMenu->addMenu(tr("Recent &Files"));
    updateRecentFiles();

    m_fileMenu->addSeparator();
    m_fileMenu->addAction(m_exitAction);

    // Edit menu
    m_editMenu = menuBar()->addMenu(tr("&Edit"));
    m_editMenu->addAction(m_undoAction);
    m_editMenu->addAction(m_redoAction);
    m_editMenu->addSeparator();
    m_editMenu->addAction(m_cutAction);
    m_editMenu->addAction(m_copyAction);
    m_editMenu->addAction(m_pasteAction);
    m_editMenu->addSeparator();
    m_editMenu->addAction(m_selectAllAction);
    m_editMenu->addAction(m_deselectAllAction);

    // View menu
    m_viewMenu = menuBar()->addMenu(tr("&View"));
    m_viewMenu->addAction(m_zoomInAction);
    m_viewMenu->addAction(m_zoomOutAction);
    m_viewMenu->addAction(m_zoomFitAction);
    m_viewMenu->addAction(m_zoomActualAction);
    m_viewMenu->addSeparator();
    m_viewMenu->addAction(m_resetViewAction);
    m_viewMenu->addSeparator();
    m_viewMenu->addAction(m_fullScreenAction);

    // Tools menu
    m_toolsMenu = menuBar()->addMenu(tr("&Tools"));
    m_toolsMenu->addAction(m_cropAction);
    m_toolsMenu->addAction(m_resizeAction);
    m_toolsMenu->addSeparator();
    m_toolsMenu->addAction(m_colorAdjustAction);
    m_toolsMenu->addSeparator();
    m_toolsMenu->addAction(m_blurAction);
    m_toolsMenu->addAction(m_sharpenAction);
    m_toolsMenu->addAction(m_filtersAction);

    // Help menu
    m_helpMenu = menuBar()->addMenu(tr("&Help"));
    m_helpMenu->addAction(m_helpAction);
    m_helpMenu->addSeparator();
    m_helpMenu->addAction(m_aboutAction);
}

void MainWindow::createToolBars()
{
    // File toolbar
    m_fileToolBar = addToolBar(tr("File"));
    m_fileToolBar->setObjectName("FileToolBar");
    m_fileToolBar->addAction(m_newAction);
    m_fileToolBar->addAction(m_openAction);
    m_fileToolBar->addAction(m_saveAction);
    m_fileToolBar->addSeparator();
    m_fileToolBar->addAction(m_importAction);
    m_fileToolBar->addAction(m_exportAction);

    // Edit toolbar
    m_editToolBar = addToolBar(tr("Edit"));
    m_editToolBar->setObjectName("EditToolBar");
    m_editToolBar->addAction(m_undoAction);
    m_editToolBar->addAction(m_redoAction);
    m_editToolBar->addSeparator();
    m_editToolBar->addAction(m_cutAction);
    m_editToolBar->addAction(m_copyAction);
    m_editToolBar->addAction(m_pasteAction);

    // View toolbar
    m_viewToolBar = addToolBar(tr("View"));
    m_viewToolBar->setObjectName("ViewToolBar");
    m_viewToolBar->addAction(m_zoomInAction);
    m_viewToolBar->addAction(m_zoomOutAction);
    m_viewToolBar->addAction(m_zoomFitAction);
    m_viewToolBar->addAction(m_zoomActualAction);

    // Tools toolbar
    m_toolsToolBar = addToolBar(tr("Tools"));
    m_toolsToolBar->setObjectName("ToolsToolBar");
    m_toolsToolBar->addAction(m_cropAction);
    m_toolsToolBar->addAction(m_resizeAction);
    m_toolsToolBar->addAction(m_colorAdjustAction);
    m_toolsToolBar->addSeparator();
    m_toolsToolBar->addAction(m_blurAction);
    m_toolsToolBar->addAction(m_sharpenAction);
    m_toolsToolBar->addAction(m_filtersAction);
}

void MainWindow::createStatusBar()
{
    m_statusLabel = new QLabel(tr("Ready"));
    statusBar()->addWidget(m_statusLabel, 1);

    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    statusBar()->addWidget(m_progressBar);

    m_positionLabel = new QLabel(tr("Position: (0, 0)"));
    statusBar()->addPermanentWidget(m_positionLabel);

    m_sizeLabel = new QLabel(tr("Size: 0 x 0"));
    statusBar()->addPermanentWidget(m_sizeLabel);

    m_zoomLabel = new QLabel(tr("Zoom: 100%"));
    statusBar()->addPermanentWidget(m_zoomLabel);
}

void MainWindow::setupCentralWidget()
{
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);

    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal, m_centralWidget);

    // Create image canvas
    m_imageCanvas = new ImageCanvas(this);

    // Create scroll area for the canvas
    m_scrollArea = new QScrollArea();
    m_scrollArea->setWidget(m_imageCanvas);
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setAlignment(Qt::AlignCenter);

    // Add scroll area to splitter
    m_mainSplitter->addWidget(m_scrollArea);

    // Set up layout
    QHBoxLayout *layout = new QHBoxLayout(m_centralWidget);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addWidget(m_mainSplitter);
}

void MainWindow::createDockWidgets()
{
    // Layer Manager Dock
    m_layerDock = new QDockWidget(tr("Layers"), this);
    m_layerDock->setObjectName("LayerDock");
    m_layerManager = new LayerManager(this);
    m_layerDock->setWidget(m_layerManager);
    addDockWidget(Qt::RightDockWidgetArea, m_layerDock);

    // Tool Palette Dock
    m_toolDock = new QDockWidget(tr("Tools"), this);
    m_toolDock->setObjectName("ToolDock");
    m_toolPalette = new ToolPalette(this);
    m_toolDock->setWidget(m_toolPalette);
    addDockWidget(Qt::LeftDockWidgetArea, m_toolDock);

    // History Dock (Undo/Redo)
    m_historyDock = new QDockWidget(tr("History"), this);
    m_historyDock->setObjectName("HistoryDock");
    m_undoView = new QUndoView(m_undoStack);
    m_historyDock->setWidget(m_undoView);
    addDockWidget(Qt::LeftDockWidgetArea, m_historyDock);

    // Properties Dock
    m_propertiesDock = new QDockWidget(tr("Properties"), this);
    m_propertiesDock->setObjectName("PropertiesDock");

    QWidget *propertiesWidget = new QWidget();
    QVBoxLayout *propsLayout = new QVBoxLayout(propertiesWidget);

    // Image properties group
    m_imagePropsGroup = new QGroupBox(tr("Image Properties"));
    // Layout will be populated when image properties are implemented

    // Color adjustment group
    m_colorAdjustGroup = new QGroupBox(tr("Color Adjustments"));
    QGridLayout *colorLayout = new QGridLayout(m_colorAdjustGroup);

    // Brightness
    colorLayout->addWidget(new QLabel(tr("Brightness:")), 0, 0);
    m_brightnessSlider = new QSlider(Qt::Horizontal);
    m_brightnessSlider->setRange(-100, 100);
    m_brightnessSlider->setValue(0);
    m_brightnessSpinBox = new QSpinBox();
    m_brightnessSpinBox->setRange(-100, 100);
    m_brightnessSpinBox->setValue(0);
    colorLayout->addWidget(m_brightnessSlider, 0, 1);
    colorLayout->addWidget(m_brightnessSpinBox, 0, 2);

    // Contrast
    colorLayout->addWidget(new QLabel(tr("Contrast:")), 1, 0);
    m_contrastSlider = new QSlider(Qt::Horizontal);
    m_contrastSlider->setRange(-100, 100);
    m_contrastSlider->setValue(0);
    m_contrastSpinBox = new QSpinBox();
    m_contrastSpinBox->setRange(-100, 100);
    m_contrastSpinBox->setValue(0);
    colorLayout->addWidget(m_contrastSlider, 1, 1);
    colorLayout->addWidget(m_contrastSpinBox, 1, 2);

    // Saturation
    colorLayout->addWidget(new QLabel(tr("Saturation:")), 2, 0);
    m_saturationSlider = new QSlider(Qt::Horizontal);
    m_saturationSlider->setRange(-100, 100);
    m_saturationSlider->setValue(0);
    m_saturationSpinBox = new QSpinBox();
    m_saturationSpinBox->setRange(-100, 100);
    m_saturationSpinBox->setValue(0);
    colorLayout->addWidget(m_saturationSlider, 2, 1);
    colorLayout->addWidget(m_saturationSpinBox, 2, 2);

    propsLayout->addWidget(m_imagePropsGroup);
    propsLayout->addWidget(m_colorAdjustGroup);
    propsLayout->addStretch();

    m_propertiesDock->setWidget(propertiesWidget);
    addDockWidget(Qt::RightDockWidgetArea, m_propertiesDock);

    // Tabify some dock widgets
    tabifyDockWidget(m_layerDock, m_propertiesDock);
    tabifyDockWidget(m_toolDock, m_historyDock);
}

void MainWindow::connectSignals()
{
    // Connect brightness controls
    connect(m_brightnessSlider, &QSlider::valueChanged, m_brightnessSpinBox, &QSpinBox::setValue);
    connect(m_brightnessSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), m_brightnessSlider, &QSlider::setValue);

    // Connect contrast controls
    connect(m_contrastSlider, &QSlider::valueChanged, m_contrastSpinBox, &QSpinBox::setValue);
    connect(m_contrastSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), m_contrastSlider, &QSlider::setValue);

    // Connect saturation controls
    connect(m_saturationSlider, &QSlider::valueChanged, m_saturationSpinBox, &QSpinBox::setValue);
    connect(m_saturationSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), m_saturationSlider, &QSlider::setValue);

    // Connect image canvas signals
    if (m_imageCanvas) {
        connect(m_imageCanvas, &ImageCanvas::imageChanged, this, &MainWindow::onImageChanged);
        connect(m_imageCanvas, &ImageCanvas::selectionChanged, this, &MainWindow::onSelectionChanged);
        connect(m_imageCanvas, &ImageCanvas::zoomChanged, this, &MainWindow::updateZoomLabel);
    }
}

// File menu slot implementations
void MainWindow::newProject()
{
    if (maybeSave()) {
        m_imageCanvas->newImage();
        m_currentFile.clear();
        m_isModified = false;
        setWindowTitle("PhotoEditor - Untitled");
        updateStatusBar();
    }
}

void MainWindow::openProject()
{
    if (maybeSave()) {
        QString fileName = QFileDialog::getOpenFileName(this,
            tr("Open Project"), QString(),
            tr("PhotoEditor Projects (*.pep);;All Files (*)"));

        if (!fileName.isEmpty()) {
            if (m_projectManager->loadProject(fileName)) {
                m_currentFile = fileName;
                m_isModified = false;
                setWindowTitle(QString("PhotoEditor - %1").arg(QFileInfo(fileName).baseName()));
                updateRecentFiles();
                updateStatusBar();
            } else {
                QMessageBox::warning(this, tr("PhotoEditor"),
                    tr("Cannot open project %1.").arg(fileName));
            }
        }
    }
}

void MainWindow::saveProject()
{
    if (m_currentFile.isEmpty()) {
        saveProjectAs();
    } else {
        if (m_projectManager->saveProject(m_currentFile)) {
            m_isModified = false;
            setWindowTitle(QString("PhotoEditor - %1").arg(QFileInfo(m_currentFile).baseName()));
            updateStatusBar();
        } else {
            QMessageBox::warning(this, tr("PhotoEditor"),
                tr("Cannot save project %1.").arg(m_currentFile));
        }
    }
}

void MainWindow::saveProjectAs()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        tr("Save Project As"), QString(),
        tr("PhotoEditor Projects (*.pep);;All Files (*)"));

    if (!fileName.isEmpty()) {
        if (m_projectManager->saveProject(fileName)) {
            m_currentFile = fileName;
            m_isModified = false;
            setWindowTitle(QString("PhotoEditor - %1").arg(QFileInfo(fileName).baseName()));
            updateRecentFiles();
            updateStatusBar();
        } else {
            QMessageBox::warning(this, tr("PhotoEditor"),
                tr("Cannot save project %1.").arg(fileName));
        }
    }
}

void MainWindow::importImage()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        tr("Import Image"), QString(),
        tr("Images (*.png *.jpg *.jpeg *.bmp *.gif *.tiff);;All Files (*)"));

    if (!fileName.isEmpty()) {
        if (m_imageCanvas->loadImage(fileName)) {
            m_isModified = true;
            updateStatusBar();
            updateZoomLabel();
        } else {
            QMessageBox::warning(this, tr("PhotoEditor"),
                tr("Cannot import image %1.").arg(fileName));
        }
    }
}

void MainWindow::exportImage()
{
    if (!m_imageCanvas->hasImage()) {
        QMessageBox::information(this, tr("PhotoEditor"),
            tr("No image to export."));
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this,
        tr("Export Image"), QString(),
        tr("PNG Files (*.png);;JPEG Files (*.jpg);;BMP Files (*.bmp);;All Files (*)"));

    if (!fileName.isEmpty()) {
        if (m_imageCanvas->saveImage(fileName)) {
            updateStatusBar();
        } else {
            QMessageBox::warning(this, tr("PhotoEditor"),
                tr("Cannot export image %1.").arg(fileName));
        }
    }
}

void MainWindow::exitApplication()
{
    if (maybeSave()) {
        QApplication::quit();
    }
}

// Edit menu slot implementations
void MainWindow::undo()
{
    m_undoStack->undo();
}

void MainWindow::redo()
{
    m_undoStack->redo();
}

void MainWindow::cut()
{
    if (m_imageCanvas) {
        m_imageCanvas->cut();
    }
}

void MainWindow::copy()
{
    if (m_imageCanvas) {
        m_imageCanvas->copy();
    }
}

void MainWindow::paste()
{
    if (m_imageCanvas) {
        m_imageCanvas->paste();
    }
}

void MainWindow::selectAll()
{
    if (m_imageCanvas) {
        m_imageCanvas->selectAll();
    }
}

void MainWindow::deselectAll()
{
    if (m_imageCanvas) {
        m_imageCanvas->deselectAll();
    }
}

// View menu slot implementations
void MainWindow::zoomIn()
{
    if (m_imageCanvas) {
        m_imageCanvas->zoomIn();
        updateZoomLabel();
    }
}

void MainWindow::zoomOut()
{
    if (m_imageCanvas) {
        m_imageCanvas->zoomOut();
        updateZoomLabel();
    }
}

void MainWindow::zoomFit()
{
    if (m_imageCanvas) {
        m_imageCanvas->zoomFit();
        updateZoomLabel();
    }
}

void MainWindow::zoomActual()
{
    if (m_imageCanvas) {
        m_imageCanvas->zoomActual();
        updateZoomLabel();
    }
}

void MainWindow::toggleFullScreen()
{
    if (isFullScreen()) {
        showNormal();
        m_fullScreenAction->setChecked(false);
    } else {
        showFullScreen();
        m_fullScreenAction->setChecked(true);
    }
}

void MainWindow::resetView()
{
    if (m_imageCanvas) {
        m_imageCanvas->resetView();
        updateZoomLabel();
    }
}

// Tools menu slot implementations
void MainWindow::cropTool()
{
    if (m_imageCanvas) {
        m_imageCanvas->activateCropTool();
    }
}

void MainWindow::resizeTool()
{
    if (m_imageCanvas) {
        m_imageCanvas->showResizeDialog();
    }
}

void MainWindow::colorAdjustments()
{
    // Show the properties dock with color adjustments
    m_propertiesDock->show();
    m_propertiesDock->raise();
}

void MainWindow::applyBlur()
{
    if (m_imageCanvas) {
        m_imageCanvas->applyBlur();
    }
}

void MainWindow::applySharpen()
{
    if (m_imageCanvas) {
        m_imageCanvas->applySharpen();
    }
}

void MainWindow::applyFilters()
{
    if (m_imageCanvas) {
        m_imageCanvas->showFiltersDialog();
    }
}

// Help menu slot implementations
void MainWindow::showAbout()
{
    QMessageBox::about(this, tr("About PhotoEditor"),
        tr("<h2>PhotoEditor 1.0</h2>"
           "<p>A powerful photo editing application built with Qt.</p>"
           "<p>Features include:</p>"
           "<ul>"
           "<li>Project management (open/save/export)</li>"
           "<li>Layer management system</li>"
           "<li>Comprehensive tool palette</li>"
           "<li>Undo/Redo functionality</li>"
           "<li>Zoom and pan controls</li>"
           "<li>Color adjustments</li>"
           "<li>Crop and resize tools</li>"
           "<li>Various filters and effects</li>"
           "</ul>"
           "<p>Built with Qt and modern C++.</p>"));
}

void MainWindow::showHelp()
{
    QMessageBox::information(this, tr("Help"),
        tr("<h3>PhotoEditor Help</h3>"
           "<p><b>Getting Started:</b></p>"
           "<ul>"
           "<li>Use File → Import Image to load an image</li>"
           "<li>Use the Tools panel to select editing tools</li>"
           "<li>Adjust colors using the Properties panel</li>"
           "<li>Use File → Export Image to save your work</li>"
           "</ul>"
           "<p><b>Keyboard Shortcuts:</b></p>"
           "<ul>"
           "<li>Ctrl+N: New Project</li>"
           "<li>Ctrl+O: Open Project</li>"
           "<li>Ctrl+S: Save Project</li>"
           "<li>Ctrl+I: Import Image</li>"
           "<li>Ctrl+E: Export Image</li>"
           "<li>Ctrl+Z: Undo</li>"
           "<li>Ctrl+Y: Redo</li>"
           "<li>Ctrl++: Zoom In</li>"
           "<li>Ctrl+-: Zoom Out</li>"
           "<li>Ctrl+0: Fit to Window</li>"
           "<li>Ctrl+1: Actual Size</li>"
           "</ul>"));
}

// UI update slots
void MainWindow::updateZoomLabel()
{
    if (m_imageCanvas) {
        double zoom = m_imageCanvas->getZoomFactor();
        m_zoomLabel->setText(tr("Zoom: %1%").arg(qRound(zoom * 100)));
        m_zoomFactor = zoom;
    }
}

void MainWindow::updateStatusBar()
{
    if (m_imageCanvas && m_imageCanvas->hasImage()) {
        QSize imageSize = m_imageCanvas->getImageSize();
        m_sizeLabel->setText(tr("Size: %1 x %2").arg(imageSize.width()).arg(imageSize.height()));
        m_statusLabel->setText(m_isModified ? tr("Modified") : tr("Ready"));
    } else {
        m_sizeLabel->setText(tr("Size: 0 x 0"));
        m_statusLabel->setText(tr("No image loaded"));
    }
}

void MainWindow::onImageChanged()
{
    m_isModified = true;
    updateStatusBar();

    // Update window title
    QString title = "PhotoEditor - ";
    if (!m_currentFile.isEmpty()) {
        title += QFileInfo(m_currentFile).baseName();
    } else {
        title += "Untitled";
    }
    if (m_isModified) {
        title += " *";
    }
    setWindowTitle(title);
}

void MainWindow::onSelectionChanged()
{
    // Update UI based on selection state
    bool hasSelection = m_imageCanvas && m_imageCanvas->hasSelection();
    m_cutAction->setEnabled(hasSelection);
    m_copyAction->setEnabled(hasSelection);
    m_cropAction->setEnabled(hasSelection);
}

// Utility methods
void MainWindow::updateRecentFiles()
{
    m_recentFilesMenu->clear();

    for (int i = 0; i < m_recentFiles.size() && i < MaxRecentFiles; ++i) {
        QString text = tr("&%1 %2").arg(i + 1).arg(QFileInfo(m_recentFiles[i]).fileName());
        QAction *action = m_recentFilesMenu->addAction(text);
        action->setData(m_recentFiles[i]);
        connect(action, &QAction::triggered, this, [this, i]() {
            QString fileName = m_recentFiles[i];
            if (maybeSave()) {
                if (m_projectManager->loadProject(fileName)) {
                    m_currentFile = fileName;
                    m_isModified = false;
                    setWindowTitle(QString("PhotoEditor - %1").arg(QFileInfo(fileName).baseName()));
                    updateStatusBar();
                } else {
                    QMessageBox::warning(this, tr("PhotoEditor"),
                        tr("Cannot open project %1.").arg(fileName));
                    m_recentFiles.removeAt(i);
                    updateRecentFiles();
                }
            }
        });
    }

    if (m_recentFiles.isEmpty()) {
        QAction *noFilesAction = m_recentFilesMenu->addAction(tr("No recent files"));
        noFilesAction->setEnabled(false);
    }
}

void MainWindow::loadSettings()
{
    QSettings settings;

    // Window geometry and state
    restoreGeometry(settings.value("geometry").toByteArray());
    restoreState(settings.value("windowState").toByteArray());

    // Recent files
    m_recentFiles = settings.value("recentFiles").toStringList();
    updateRecentFiles();
}

void MainWindow::saveSettings()
{
    QSettings settings;

    // Window geometry and state
    settings.setValue("geometry", saveGeometry());
    settings.setValue("windowState", saveState());

    // Recent files
    settings.setValue("recentFiles", m_recentFiles);
}

bool MainWindow::maybeSave()
{
    if (m_isModified) {
        QMessageBox::StandardButton ret = QMessageBox::warning(this, tr("PhotoEditor"),
            tr("The project has been modified.\n"
               "Do you want to save your changes?"),
            QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel);

        if (ret == QMessageBox::Save) {
            saveProject();
            return !m_isModified; // Return false if save failed
        } else if (ret == QMessageBox::Cancel) {
            return false;
        }
    }
    return true;
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (maybeSave()) {
        saveSettings();
        event->accept();
    } else {
        event->ignore();
    }
}
