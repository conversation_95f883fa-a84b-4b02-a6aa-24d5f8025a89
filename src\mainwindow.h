#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QDockWidget>
#include <QScrollArea>
#include <QLabel>
#include <QSlider>
#include <QSpinBox>
#include <QUndoStack>
#include <QUndoView>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QComboBox>
#include <QCheckBox>
#include <QProgressBar>

QT_BEGIN_NAMESPACE
class QAction;
class QActionGroup;
QT_END_NAMESPACE

class ImageCanvas;
class LayerManager;
class ToolPalette;
class ProjectManager;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    // File menu actions
    void newProject();
    void openProject();
    void saveProject();
    void saveProjectAs();
    void importImage();
    void exportImage();
    void exitApplication();
    
    // Edit menu actions
    void undo();
    void redo();
    void cut();
    void copy();
    void paste();
    void selectAll();
    void deselectAll();
    
    // View menu actions
    void zoomIn();
    void zoomOut();
    void zoomFit();
    void zoomActual();
    void toggleFullScreen();
    void resetView();
    
    // Tools menu actions
    void cropTool();
    void resizeTool();
    void colorAdjustments();
    void applyBlur();
    void applySharpen();
    void applyFilters();
    
    // Help menu actions
    void showAbout();
    void showHelp();
    
    // UI update slots
    void updateZoomLabel();
    void updateStatusBar();
    void onImageChanged();
    void onSelectionChanged();

private:
    void createActions();
    void createMenus();
    void createToolBars();
    void createStatusBar();
    void createDockWidgets();
    void setupCentralWidget();
    void connectSignals();
    void updateRecentFiles();
    void loadSettings();
    void saveSettings();
    bool maybeSave();
    
    // UI Components
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    QScrollArea *m_scrollArea;
    ImageCanvas *m_imageCanvas;
    
    // Dock widgets
    QDockWidget *m_layerDock;
    QDockWidget *m_toolDock;
    QDockWidget *m_historyDock;
    QDockWidget *m_propertiesDock;
    
    LayerManager *m_layerManager;
    ToolPalette *m_toolPalette;
    QUndoView *m_undoView;
    
    // Menus
    QMenu *m_fileMenu;
    QMenu *m_editMenu;
    QMenu *m_viewMenu;
    QMenu *m_toolsMenu;
    QMenu *m_helpMenu;
    QMenu *m_recentFilesMenu;
    
    // Toolbars
    QToolBar *m_fileToolBar;
    QToolBar *m_editToolBar;
    QToolBar *m_viewToolBar;
    QToolBar *m_toolsToolBar;
    
    // Actions
    QAction *m_newAction;
    QAction *m_openAction;
    QAction *m_saveAction;
    QAction *m_saveAsAction;
    QAction *m_importAction;
    QAction *m_exportAction;
    QAction *m_exitAction;
    
    QAction *m_undoAction;
    QAction *m_redoAction;
    QAction *m_cutAction;
    QAction *m_copyAction;
    QAction *m_pasteAction;
    QAction *m_selectAllAction;
    QAction *m_deselectAllAction;
    
    QAction *m_zoomInAction;
    QAction *m_zoomOutAction;
    QAction *m_zoomFitAction;
    QAction *m_zoomActualAction;
    QAction *m_fullScreenAction;
    QAction *m_resetViewAction;
    
    QAction *m_cropAction;
    QAction *m_resizeAction;
    QAction *m_colorAdjustAction;
    QAction *m_blurAction;
    QAction *m_sharpenAction;
    QAction *m_filtersAction;
    
    QAction *m_aboutAction;
    QAction *m_helpAction;
    
    // Status bar widgets
    QLabel *m_statusLabel;
    QLabel *m_zoomLabel;
    QLabel *m_positionLabel;
    QLabel *m_sizeLabel;
    QProgressBar *m_progressBar;
    
    // Core components
    QUndoStack *m_undoStack;
    ProjectManager *m_projectManager;
    
    // Properties panel widgets
    QGroupBox *m_imagePropsGroup;
    QGroupBox *m_colorAdjustGroup;
    QSlider *m_brightnessSlider;
    QSlider *m_contrastSlider;
    QSlider *m_saturationSlider;
    QSpinBox *m_brightnessSpinBox;
    QSpinBox *m_contrastSpinBox;
    QSpinBox *m_saturationSpinBox;
    
    // Current state
    QString m_currentFile;
    bool m_isModified;
    double m_zoomFactor;
    QStringList m_recentFiles;
    
    static const int MaxRecentFiles = 5;
};

#endif // MAINWINDOW_H
