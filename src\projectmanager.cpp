#include "projectmanager.h"
#include "mainwindow.h"
#include "layermanager.h"
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QBuffer>
#include <QPixmap>
#include <QDebug>

ProjectManager::ProjectManager(QObject *parent)
    : QObject(parent)
    , m_hasUnsavedChanges(false)
    , m_projectName("Untitled")
    , m_canvasSize(800, 600)
    , m_mainWindow(nullptr)
{
    m_mainWindow = qobject_cast<MainWindow*>(parent);
}

ProjectManager::~ProjectManager()
{
}

bool ProjectManager::newProject()
{
    m_currentProjectFile.clear();
    m_hasUnsavedChanges = false;
    m_projectName = "Untitled";
    m_canvasSize = QSize(800, 600);
    
    emit projectChanged();
    return true;
}

bool ProjectManager::loadProject(const QString &fileName)
{
    if (!QFile::exists(fileName)) {
        return false;
    }
    
    if (loadProjectFromJson(fileName)) {
        m_currentProjectFile = fileName;
        m_hasUnsavedChanges = false;
        emit projectLoaded();
        return true;
    }
    
    return false;
}

bool ProjectManager::saveProject(const QString &fileName)
{
    if (saveProjectToJson(fileName)) {
        m_currentProjectFile = fileName;
        m_hasUnsavedChanges = false;
        emit projectSaved();
        return true;
    }
    
    return false;
}

bool ProjectManager::saveProjectToJson(const QString &fileName)
{
    QJsonObject projectObject;
    
    // Project metadata
    projectObject["version"] = "1.0";
    projectObject["name"] = m_projectName;
    projectObject["canvasWidth"] = m_canvasSize.width();
    projectObject["canvasHeight"] = m_canvasSize.height();
    
    // Layers (if we have access to layer manager)
    QJsonArray layersArray;
    if (m_mainWindow) {
        // Note: In a full implementation, we'd get layers from the main window
        // For now, we'll save a placeholder
        QJsonObject layerObject;
        layerObject["name"] = "Background";
        layerObject["visible"] = true;
        layerObject["opacity"] = 100;
        layerObject["blendMode"] = "Normal";
        layersArray.append(layerObject);
    }
    projectObject["layers"] = layersArray;
    
    // Save to file
    QJsonDocument document(projectObject);
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }
    
    file.write(document.toJson());
    return true;
}

bool ProjectManager::loadProjectFromJson(const QString &fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }
    
    QByteArray data = file.readAll();
    QJsonDocument document = QJsonDocument::fromJson(data);
    
    if (document.isNull() || !document.isObject()) {
        return false;
    }
    
    QJsonObject projectObject = document.object();
    
    // Load project metadata
    m_projectName = projectObject["name"].toString("Untitled");
    m_canvasSize = QSize(
        projectObject["canvasWidth"].toInt(800),
        projectObject["canvasHeight"].toInt(600)
    );
    
    // Load layers
    QJsonArray layersArray = projectObject["layers"].toArray();
    // Note: In a full implementation, we'd restore layers to the layer manager
    
    return true;
}

QJsonObject ProjectManager::layerToJson(const Layer *layer) const
{
    QJsonObject layerObject;
    
    if (!layer) {
        return layerObject;
    }
    
    layerObject["name"] = layer->name();
    layerObject["visible"] = layer->isVisible();
    layerObject["opacity"] = layer->opacity();
    layerObject["blendMode"] = layer->blendMode();
    
    // Save pixmap as base64 encoded PNG
    if (!layer->pixmap().isNull()) {
        QBuffer buffer;
        buffer.open(QIODevice::WriteOnly);
        layer->pixmap().save(&buffer, "PNG");
        layerObject["imageData"] = QString::fromLatin1(buffer.data().toBase64());
    }
    
    return layerObject;
}

Layer* ProjectManager::layerFromJson(const QJsonObject &json) const
{
    QString name = json["name"].toString();
    bool visible = json["visible"].toBool(true);
    int opacity = json["opacity"].toInt(100);
    QString blendMode = json["blendMode"].toString("Normal");
    
    // Load pixmap from base64 data
    QPixmap pixmap;
    QString imageData = json["imageData"].toString();
    if (!imageData.isEmpty()) {
        QByteArray data = QByteArray::fromBase64(imageData.toLatin1());
        pixmap.loadFromData(data, "PNG");
    }
    
    Layer *layer = new Layer(name, pixmap);
    layer->setVisible(visible);
    layer->setOpacity(opacity);
    layer->setBlendMode(blendMode);
    
    return layer;
}
