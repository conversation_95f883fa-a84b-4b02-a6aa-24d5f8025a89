#ifndef PROJECTMANAGER_H
#define PROJECTMANAGER_H

#include <QObject>
#include <QString>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>

class MainWindow;
class Layer;

class ProjectManager : public QObject
{
    Q_OBJECT

public:
    explicit ProjectManager(QObject *parent = nullptr);
    ~ProjectManager();

    // Project operations
    bool newProject();
    bool loadProject(const QString &fileName);
    bool saveProject(const QString &fileName);
    
    // Project properties
    QString currentProjectFile() const { return m_currentProjectFile; }
    bool hasUnsavedChanges() const { return m_hasUnsavedChanges; }
    void setUnsavedChanges(bool hasChanges) { m_hasUnsavedChanges = hasChanges; }
    
    // Project metadata
    QString projectName() const { return m_projectName; }
    void setProjectName(const QString &name) { m_projectName = name; }
    
    QSize canvasSize() const { return m_canvasSize; }
    void setCanvasSize(const QSize &size) { m_canvasSize = size; }

signals:
    void projectLoaded();
    void projectSaved();
    void projectChanged();

private:
    bool saveProjectToJson(const QString &fileName);
    bool loadProjectFromJson(const QString &fileName);
    QJsonObject layerToJson(const Layer *layer) const;
    Layer* layerFromJson(const QJsonObject &json) const;
    
    QString m_currentProjectFile;
    bool m_hasUnsavedChanges;
    QString m_projectName;
    QSize m_canvasSize;
    
    MainWindow *m_mainWindow;
};

#endif // PROJECTMANAGER_H
