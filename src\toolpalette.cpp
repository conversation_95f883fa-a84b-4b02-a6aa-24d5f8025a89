#include "toolpalette.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QSlider>
#include <QSpinBox>
#include <QComboBox>
#include <QToolButton>
#include <QPushButton>
#include <QButtonGroup>
#include <QColorDialog>
#include <QPixmap>
#include <QPainter>
#include <QIcon>

ToolPalette::ToolPalette(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_toolsGroup(nullptr)
    , m_toolsLayout(nullptr)
    , m_toolButtonGroup(nullptr)
    , m_selectButton(nullptr)
    , m_moveButton(nullptr)
    , m_cropButton(nullptr)
    , m_paintBrushButton(nullptr)
    , m_eraserButton(nullptr)
    , m_panButton(nullptr)
    , m_zoomButton(nullptr)
    , m_eyedropperButton(nullptr)
    , m_brushGroup(nullptr)
    , m_brushSizeLabel(nullptr)
    , m_brushSizeSlider(nullptr)
    , m_brushSizeSpinBox(nullptr)
    , m_brushOpacityLabel(nullptr)
    , m_brushOpacitySlider(nullptr)
    , m_brushOpacitySpinBox(nullptr)
    , m_brushShapeCombo(nullptr)
    , m_colorGroup(nullptr)
    , m_foregroundColorButton(nullptr)
    , m_backgroundColorButton(nullptr)
    , m_swapColorsButton(nullptr)
    , m_resetColorsButton(nullptr)
    , m_currentTool(SelectTool)
    , m_brushSize(10)
    , m_brushOpacity(100)
    , m_foregroundColor(Qt::black)
    , m_backgroundColor(Qt::white)
    , m_brushShape("Round")
{
    setupUI();
    connectSignals();
    updateToolButtons();
    updateColorButtons();
}

ToolPalette::~ToolPalette()
{
}

void ToolPalette::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(10);
    
    createToolButtons();
    createBrushControls();
    createColorControls();
    
    m_mainLayout->addStretch();
}

void ToolPalette::createToolButtons()
{
    m_toolsGroup = new QGroupBox(tr("Tools"));
    m_toolsLayout = new QGridLayout(m_toolsGroup);
    m_toolsLayout->setSpacing(2);
    
    m_toolButtonGroup = new QButtonGroup(this);
    m_toolButtonGroup->setExclusive(true);
    
    // Create tool buttons
    m_selectButton = new QToolButton();
    m_selectButton->setText("S");
    m_selectButton->setToolTip(tr("Select Tool"));
    m_selectButton->setCheckable(true);
    m_selectButton->setChecked(true);
    m_selectButton->setMinimumSize(40, 40);
    m_toolButtonGroup->addButton(m_selectButton, SelectTool);
    
    m_moveButton = new QToolButton();
    m_moveButton->setText("M");
    m_moveButton->setToolTip(tr("Move Tool"));
    m_moveButton->setCheckable(true);
    m_moveButton->setMinimumSize(40, 40);
    m_toolButtonGroup->addButton(m_moveButton, MoveTool);
    
    m_cropButton = new QToolButton();
    m_cropButton->setText("C");
    m_cropButton->setToolTip(tr("Crop Tool"));
    m_cropButton->setCheckable(true);
    m_cropButton->setMinimumSize(40, 40);
    m_toolButtonGroup->addButton(m_cropButton, CropTool);
    
    m_paintBrushButton = new QToolButton();
    m_paintBrushButton->setText("B");
    m_paintBrushButton->setToolTip(tr("Paint Brush"));
    m_paintBrushButton->setCheckable(true);
    m_paintBrushButton->setMinimumSize(40, 40);
    m_toolButtonGroup->addButton(m_paintBrushButton, PaintBrushTool);
    
    m_eraserButton = new QToolButton();
    m_eraserButton->setText("E");
    m_eraserButton->setToolTip(tr("Eraser"));
    m_eraserButton->setCheckable(true);
    m_eraserButton->setMinimumSize(40, 40);
    m_toolButtonGroup->addButton(m_eraserButton, EraserTool);
    
    m_panButton = new QToolButton();
    m_panButton->setText("H");
    m_panButton->setToolTip(tr("Pan Tool"));
    m_panButton->setCheckable(true);
    m_panButton->setMinimumSize(40, 40);
    m_toolButtonGroup->addButton(m_panButton, PanTool);
    
    m_zoomButton = new QToolButton();
    m_zoomButton->setText("Z");
    m_zoomButton->setToolTip(tr("Zoom Tool"));
    m_zoomButton->setCheckable(true);
    m_zoomButton->setMinimumSize(40, 40);
    m_toolButtonGroup->addButton(m_zoomButton, ZoomTool);
    
    m_eyedropperButton = new QToolButton();
    m_eyedropperButton->setText("I");
    m_eyedropperButton->setToolTip(tr("Eyedropper"));
    m_eyedropperButton->setCheckable(true);
    m_eyedropperButton->setMinimumSize(40, 40);
    m_toolButtonGroup->addButton(m_eyedropperButton, EyedropperTool);
    
    // Layout tool buttons in a 2x4 grid
    m_toolsLayout->addWidget(m_selectButton, 0, 0);
    m_toolsLayout->addWidget(m_moveButton, 0, 1);
    m_toolsLayout->addWidget(m_cropButton, 1, 0);
    m_toolsLayout->addWidget(m_paintBrushButton, 1, 1);
    m_toolsLayout->addWidget(m_eraserButton, 2, 0);
    m_toolsLayout->addWidget(m_panButton, 2, 1);
    m_toolsLayout->addWidget(m_zoomButton, 3, 0);
    m_toolsLayout->addWidget(m_eyedropperButton, 3, 1);
    
    m_mainLayout->addWidget(m_toolsGroup);
}

void ToolPalette::createBrushControls()
{
    m_brushGroup = new QGroupBox(tr("Brush"));
    QVBoxLayout *brushLayout = new QVBoxLayout(m_brushGroup);
    
    // Brush size
    m_brushSizeLabel = new QLabel(tr("Size: %1").arg(m_brushSize));
    brushLayout->addWidget(m_brushSizeLabel);
    
    m_brushSizeSlider = new QSlider(Qt::Horizontal);
    m_brushSizeSlider->setRange(1, 100);
    m_brushSizeSlider->setValue(m_brushSize);
    brushLayout->addWidget(m_brushSizeSlider);
    
    m_brushSizeSpinBox = new QSpinBox();
    m_brushSizeSpinBox->setRange(1, 100);
    m_brushSizeSpinBox->setValue(m_brushSize);
    brushLayout->addWidget(m_brushSizeSpinBox);
    
    // Brush opacity
    m_brushOpacityLabel = new QLabel(tr("Opacity: %1%").arg(m_brushOpacity));
    brushLayout->addWidget(m_brushOpacityLabel);
    
    m_brushOpacitySlider = new QSlider(Qt::Horizontal);
    m_brushOpacitySlider->setRange(1, 100);
    m_brushOpacitySlider->setValue(m_brushOpacity);
    brushLayout->addWidget(m_brushOpacitySlider);
    
    m_brushOpacitySpinBox = new QSpinBox();
    m_brushOpacitySpinBox->setRange(1, 100);
    m_brushOpacitySpinBox->setValue(m_brushOpacity);
    m_brushOpacitySpinBox->setSuffix("%");
    brushLayout->addWidget(m_brushOpacitySpinBox);
    
    // Brush shape
    QLabel *shapeLabel = new QLabel(tr("Shape:"));
    brushLayout->addWidget(shapeLabel);
    
    m_brushShapeCombo = new QComboBox();
    m_brushShapeCombo->addItems({tr("Round"), tr("Square"), tr("Soft Round")});
    brushLayout->addWidget(m_brushShapeCombo);
    
    m_mainLayout->addWidget(m_brushGroup);
}

void ToolPalette::createColorControls()
{
    m_colorGroup = new QGroupBox(tr("Colors"));
    QVBoxLayout *colorLayout = new QVBoxLayout(m_colorGroup);
    
    // Color buttons layout
    QHBoxLayout *colorButtonsLayout = new QHBoxLayout();
    
    // Foreground color button
    m_foregroundColorButton = new QPushButton();
    m_foregroundColorButton->setMinimumSize(40, 40);
    m_foregroundColorButton->setMaximumSize(40, 40);
    m_foregroundColorButton->setToolTip(tr("Foreground Color"));
    colorButtonsLayout->addWidget(m_foregroundColorButton);
    
    // Background color button
    m_backgroundColorButton = new QPushButton();
    m_backgroundColorButton->setMinimumSize(40, 40);
    m_backgroundColorButton->setMaximumSize(40, 40);
    m_backgroundColorButton->setToolTip(tr("Background Color"));
    colorButtonsLayout->addWidget(m_backgroundColorButton);
    
    colorLayout->addLayout(colorButtonsLayout);
    
    // Color action buttons
    QHBoxLayout *colorActionsLayout = new QHBoxLayout();
    
    m_swapColorsButton = new QPushButton(tr("Swap"));
    m_swapColorsButton->setToolTip(tr("Swap foreground and background colors"));
    colorActionsLayout->addWidget(m_swapColorsButton);
    
    m_resetColorsButton = new QPushButton(tr("Reset"));
    m_resetColorsButton->setToolTip(tr("Reset to default colors"));
    colorActionsLayout->addWidget(m_resetColorsButton);
    
    colorLayout->addLayout(colorActionsLayout);
    
    m_mainLayout->addWidget(m_colorGroup);
}

void ToolPalette::connectSignals()
{
    // Tool button signals
    connect(m_toolButtonGroup, QOverload<int>::of(&QButtonGroup::buttonClicked),
            this, &ToolPalette::onToolButtonClicked);
    
    // Brush size signals
    connect(m_brushSizeSlider, &QSlider::valueChanged, m_brushSizeSpinBox, &QSpinBox::setValue);
    connect(m_brushSizeSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), 
            m_brushSizeSlider, &QSlider::setValue);
    connect(m_brushSizeSlider, &QSlider::valueChanged, this, &ToolPalette::onBrushSizeChanged);
    
    // Brush opacity signals
    connect(m_brushOpacitySlider, &QSlider::valueChanged, m_brushOpacitySpinBox, &QSpinBox::setValue);
    connect(m_brushOpacitySpinBox, QOverload<int>::of(&QSpinBox::valueChanged), 
            m_brushOpacitySlider, &QSlider::setValue);
    connect(m_brushOpacitySlider, &QSlider::valueChanged, this, &ToolPalette::onBrushOpacityChanged);
    
    // Color signals
    connect(m_foregroundColorButton, &QPushButton::clicked, this, &ToolPalette::onForegroundColorClicked);
    connect(m_backgroundColorButton, &QPushButton::clicked, this, &ToolPalette::onBackgroundColorClicked);
    connect(m_swapColorsButton, &QPushButton::clicked, this, &ToolPalette::swapColors);
    connect(m_resetColorsButton, &QPushButton::clicked, this, &ToolPalette::resetColors);
}

void ToolPalette::setCurrentTool(Tool tool)
{
    if (m_currentTool != tool) {
        m_currentTool = tool;
        updateToolButtons();
        emit toolChanged(tool);
    }
}

void ToolPalette::setBrushSize(int size)
{
    size = qBound(1, size, 100);
    if (m_brushSize != size) {
        m_brushSize = size;
        m_brushSizeSlider->blockSignals(true);
        m_brushSizeSpinBox->blockSignals(true);
        m_brushSizeSlider->setValue(size);
        m_brushSizeSpinBox->setValue(size);
        m_brushSizeSlider->blockSignals(false);
        m_brushSizeSpinBox->blockSignals(false);
        m_brushSizeLabel->setText(tr("Size: %1").arg(size));
        emit brushSizeChanged(size);
    }
}

void ToolPalette::setBrushOpacity(int opacity)
{
    opacity = qBound(1, opacity, 100);
    if (m_brushOpacity != opacity) {
        m_brushOpacity = opacity;
        m_brushOpacitySlider->blockSignals(true);
        m_brushOpacitySpinBox->blockSignals(true);
        m_brushOpacitySlider->setValue(opacity);
        m_brushOpacitySpinBox->setValue(opacity);
        m_brushOpacitySlider->blockSignals(false);
        m_brushOpacitySpinBox->blockSignals(false);
        m_brushOpacityLabel->setText(tr("Opacity: %1%").arg(opacity));
        emit brushOpacityChanged(opacity);
    }
}

void ToolPalette::setForegroundColor(const QColor &color)
{
    if (m_foregroundColor != color) {
        m_foregroundColor = color;
        updateColorButtons();
        emit foregroundColorChanged(color);
    }
}

void ToolPalette::setBackgroundColor(const QColor &color)
{
    if (m_backgroundColor != color) {
        m_backgroundColor = color;
        updateColorButtons();
        emit backgroundColorChanged(color);
    }
}

// Slot implementations
void ToolPalette::onToolButtonClicked()
{
    int toolId = m_toolButtonGroup->checkedId();
    if (toolId >= 0) {
        setCurrentTool(static_cast<Tool>(toolId));
    }
}

void ToolPalette::onBrushSizeChanged()
{
    int size = m_brushSizeSlider->value();
    setBrushSize(size);
}

void ToolPalette::onBrushOpacityChanged()
{
    int opacity = m_brushOpacitySlider->value();
    setBrushOpacity(opacity);
}

void ToolPalette::onForegroundColorClicked()
{
    QColor color = QColorDialog::getColor(m_foregroundColor, this, tr("Choose Foreground Color"));
    if (color.isValid()) {
        setForegroundColor(color);
    }
}

void ToolPalette::onBackgroundColorClicked()
{
    QColor color = QColorDialog::getColor(m_backgroundColor, this, tr("Choose Background Color"));
    if (color.isValid()) {
        setBackgroundColor(color);
    }
}

void ToolPalette::swapColors()
{
    QColor temp = m_foregroundColor;
    setForegroundColor(m_backgroundColor);
    setBackgroundColor(temp);
}

void ToolPalette::resetColors()
{
    setForegroundColor(Qt::black);
    setBackgroundColor(Qt::white);
}

void ToolPalette::updateToolButtons()
{
    // Update the checked state of tool buttons
    QAbstractButton *button = m_toolButtonGroup->button(m_currentTool);
    if (button) {
        button->setChecked(true);
    }
}

void ToolPalette::updateColorButtons()
{
    // Create color icons for the buttons
    QPixmap foregroundPixmap(32, 32);
    foregroundPixmap.fill(m_foregroundColor);
    m_foregroundColorButton->setIcon(QIcon(foregroundPixmap));
    m_foregroundColorButton->setStyleSheet(
        QString("QPushButton { background-color: %1; border: 2px solid #555; }")
        .arg(m_foregroundColor.name()));

    QPixmap backgroundPixmap(32, 32);
    backgroundPixmap.fill(m_backgroundColor);
    m_backgroundColorButton->setIcon(QIcon(backgroundPixmap));
    m_backgroundColorButton->setStyleSheet(
        QString("QPushButton { background-color: %1; border: 2px solid #555; }")
        .arg(m_backgroundColor.name()));
}
