#ifndef TOOLPALETTE_H
#define TOOLPALETTE_H

#include <QWidget>
#include <QButtonGroup>
#include <QToolButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QSlider>
#include <QSpinBox>
#include <QComboBox>
#include <QColorDialog>
#include <QPushButton>

class ToolPalette : public QWidget
{
    Q_OBJECT

public:
    enum Tool {
        SelectTool,
        MoveTool,
        CropTool,
        PaintBrushTool,
        EraserTool,
        PanTool,
        ZoomTool,
        EyedropperTool
    };

    explicit ToolPalette(QWidget *parent = nullptr);
    ~ToolPalette();

    Tool currentTool() const { return m_currentTool; }
    void setCurrentTool(Tool tool);

    // Brush properties
    int brushSize() const { return m_brushSize; }
    void setBrushSize(int size);
    
    int brushOpacity() const { return m_brushOpacity; }
    void setBrushOpacity(int opacity);
    
    QColor foregroundColor() const { return m_foregroundColor; }
    void setForegroundColor(const QColor &color);
    
    QColor backgroundColor() const { return m_backgroundColor; }
    void setBackgroundColor(const QColor &color);

signals:
    void toolChanged(ToolPalette::Tool tool);
    void brushSizeChanged(int size);
    void brushOpacityChanged(int opacity);
    void foregroundColorChanged(const QColor &color);
    void backgroundColorChanged(const QColor &color);

private slots:
    void onToolButtonClicked();
    void onBrushSizeChanged();
    void onBrushOpacityChanged();
    void onForegroundColorClicked();
    void onBackgroundColorClicked();
    void swapColors();
    void resetColors();

private:
    void setupUI();
    void createToolButtons();
    void createBrushControls();
    void createColorControls();
    void connectSignals();
    void updateToolButtons();
    void updateColorButtons();
    
    // UI Components
    QVBoxLayout *m_mainLayout;
    
    // Tool buttons
    QGroupBox *m_toolsGroup;
    QGridLayout *m_toolsLayout;
    QButtonGroup *m_toolButtonGroup;
    
    QToolButton *m_selectButton;
    QToolButton *m_moveButton;
    QToolButton *m_cropButton;
    QToolButton *m_paintBrushButton;
    QToolButton *m_eraserButton;
    QToolButton *m_panButton;
    QToolButton *m_zoomButton;
    QToolButton *m_eyedropperButton;
    
    // Brush controls
    QGroupBox *m_brushGroup;
    QLabel *m_brushSizeLabel;
    QSlider *m_brushSizeSlider;
    QSpinBox *m_brushSizeSpinBox;
    QLabel *m_brushOpacityLabel;
    QSlider *m_brushOpacitySlider;
    QSpinBox *m_brushOpacitySpinBox;
    QComboBox *m_brushShapeCombo;
    
    // Color controls
    QGroupBox *m_colorGroup;
    QPushButton *m_foregroundColorButton;
    QPushButton *m_backgroundColorButton;
    QPushButton *m_swapColorsButton;
    QPushButton *m_resetColorsButton;
    
    // Current state
    Tool m_currentTool;
    int m_brushSize;
    int m_brushOpacity;
    QColor m_foregroundColor;
    QColor m_backgroundColor;
    QString m_brushShape;
};

#endif // TOOLPALETTE_H
