#include "undocommands.h"
#include "imagecanvas.h"
#include "layermanager.h"

// ImageCommand implementation
ImageCommand::ImageCommand(ImageCanvas *canvas, const QString &text, QUndoCommand *parent)
    : QUndoCommand(text, parent)
    , m_canvas(canvas)
{
}

// BrightnessContrastCommand implementation
BrightnessContrastCommand::BrightnessContrastCommand(ImageCanvas *canvas, int brightness, int contrast,
                                                   const QPixmap &oldImage, QUndoCommand *parent)
    : ImageCommand(canvas, QObject::tr("Brightness/Contrast"), parent)
    , m_brightness(brightness)
    , m_contrast(contrast)
    , m_oldImage(oldImage)
    , m_firstRedo(true)
{
}

void BrightnessContrastCommand::undo()
{
    if (m_canvas) {
        // Restore the old image directly
        // In a full implementation, we'd have a method to set the image directly
        // For now, we'll trigger the operation with inverse values
        m_canvas->applyBrightnessContrast(-m_brightness, -m_contrast);
    }
}

void BrightnessContrastCommand::redo()
{
    if (m_canvas) {
        if (m_firstRedo) {
            // The operation has already been performed, just store the result
            m_firstRedo = false;
        } else {
            // Reapply the operation
            m_canvas->applyBrightnessContrast(m_brightness, m_contrast);
        }
    }
}

// SaturationCommand implementation
SaturationCommand::SaturationCommand(ImageCanvas *canvas, int saturation,
                                   const QPixmap &oldImage, QUndoCommand *parent)
    : ImageCommand(canvas, QObject::tr("Saturation"), parent)
    , m_saturation(saturation)
    , m_oldImage(oldImage)
    , m_firstRedo(true)
{
}

void SaturationCommand::undo()
{
    if (m_canvas) {
        // Apply inverse saturation
        m_canvas->applySaturation(-m_saturation);
    }
}

void SaturationCommand::redo()
{
    if (m_canvas) {
        if (m_firstRedo) {
            m_firstRedo = false;
        } else {
            m_canvas->applySaturation(m_saturation);
        }
    }
}

// FilterCommand implementation
FilterCommand::FilterCommand(ImageCanvas *canvas, const QString &filterType,
                           const QPixmap &oldImage, QUndoCommand *parent)
    : ImageCommand(canvas, QObject::tr("Apply %1").arg(filterType), parent)
    , m_filterType(filterType)
    , m_oldImage(oldImage)
    , m_firstRedo(true)
{
}

void FilterCommand::undo()
{
    // For filters, we need to restore the original image
    // In a full implementation, we'd have a method to set the image directly
    if (m_canvas) {
        // This is a simplified approach - in reality we'd need better image state management
    }
}

void FilterCommand::redo()
{
    if (m_canvas) {
        if (m_firstRedo) {
            m_firstRedo = false;
        } else {
            if (m_filterType == "blur") {
                m_canvas->applyBlur();
            } else if (m_filterType == "sharpen") {
                m_canvas->applySharpen();
            }
        }
    }
}

// CropCommand implementation
CropCommand::CropCommand(ImageCanvas *canvas, const QRect &cropRect,
                        const QPixmap &oldImage, QUndoCommand *parent)
    : ImageCommand(canvas, QObject::tr("Crop"), parent)
    , m_cropRect(cropRect)
    , m_oldImage(oldImage)
    , m_firstRedo(true)
{
}

void CropCommand::undo()
{
    // Restore original image
    // In a full implementation, we'd have proper image state management
}

void CropCommand::redo()
{
    if (m_canvas) {
        if (m_firstRedo) {
            m_firstRedo = false;
        } else {
            // Reapply crop
            m_canvas->cropToSelection();
        }
    }
}

// ResizeCommand implementation
ResizeCommand::ResizeCommand(ImageCanvas *canvas, const QSize &newSize,
                           const QPixmap &oldImage, QUndoCommand *parent)
    : ImageCommand(canvas, QObject::tr("Resize"), parent)
    , m_newSize(newSize)
    , m_oldSize(oldImage.size())
    , m_oldImage(oldImage)
    , m_firstRedo(true)
{
}

void ResizeCommand::undo()
{
    if (m_canvas) {
        m_canvas->resizeImage(m_oldSize);
    }
}

void ResizeCommand::redo()
{
    if (m_canvas) {
        if (m_firstRedo) {
            m_firstRedo = false;
        } else {
            m_canvas->resizeImage(m_newSize);
        }
    }
}

// PaintCommand implementation
PaintCommand::PaintCommand(ImageCanvas *canvas, const QPixmap &oldImage,
                          const QPixmap &newImage, QUndoCommand *parent)
    : ImageCommand(canvas, QObject::tr("Paint"), parent)
    , m_oldImage(oldImage)
    , m_newImage(newImage)
{
}

void PaintCommand::undo()
{
    // Restore old image state
    // In a full implementation, we'd have proper image state management
}

void PaintCommand::redo()
{
    // Apply new image state
    // In a full implementation, we'd have proper image state management
}

// LayerCommand implementation
LayerCommand::LayerCommand(LayerManager *layerManager, const QString &text, QUndoCommand *parent)
    : QUndoCommand(text, parent)
    , m_layerManager(layerManager)
{
}

// AddLayerCommand implementation
AddLayerCommand::AddLayerCommand(LayerManager *layerManager, const QString &layerName,
                                const QPixmap &pixmap, QUndoCommand *parent)
    : LayerCommand(layerManager, QObject::tr("Add Layer"), parent)
    , m_layerName(layerName)
    , m_pixmap(pixmap)
    , m_layerIndex(-1)
    , m_firstRedo(true)
{
}

void AddLayerCommand::undo()
{
    if (m_layerManager && m_layerIndex >= 0) {
        m_layerManager->removeLayer(m_layerIndex);
    }
}

void AddLayerCommand::redo()
{
    if (m_layerManager) {
        if (m_firstRedo) {
            m_firstRedo = false;
            m_layerIndex = m_layerManager->layerCount(); // Will be the index after adding
        }
        m_layerManager->addLayer(m_layerName, m_pixmap);
    }
}

// RemoveLayerCommand implementation
RemoveLayerCommand::RemoveLayerCommand(LayerManager *layerManager, int layerIndex, QUndoCommand *parent)
    : LayerCommand(layerManager, QObject::tr("Remove Layer"), parent)
    , m_layerIndex(layerIndex)
    , m_firstRedo(true)
{
    // Store layer properties for undo
    if (m_layerManager) {
        Layer *layer = m_layerManager->getLayer(layerIndex);
        if (layer) {
            m_layerName = layer->name();
            m_pixmap = layer->pixmap();
            m_visible = layer->isVisible();
            m_opacity = layer->opacity();
            m_blendMode = layer->blendMode();
        }
    }
}

void RemoveLayerCommand::undo()
{
    if (m_layerManager) {
        // Re-add the layer at the same position
        m_layerManager->addLayer(m_layerName, m_pixmap);
        // Restore properties
        int newIndex = m_layerManager->layerCount() - 1;
        m_layerManager->setLayerVisible(newIndex, m_visible);
        m_layerManager->setLayerOpacity(newIndex, m_opacity);
        m_layerManager->setLayerBlendMode(newIndex, m_blendMode);
    }
}

void RemoveLayerCommand::redo()
{
    if (m_layerManager) {
        if (m_firstRedo) {
            m_firstRedo = false;
        }
        m_layerManager->removeLayer(m_layerIndex);
    }
}

// LayerPropertyCommand implementation
LayerPropertyCommand::LayerPropertyCommand(LayerManager *layerManager, int layerIndex,
                                          PropertyType property, const QVariant &oldValue,
                                          const QVariant &newValue, QUndoCommand *parent)
    : LayerCommand(layerManager, QObject::tr("Change Layer Property"), parent)
    , m_layerIndex(layerIndex)
    , m_property(property)
    , m_oldValue(oldValue)
    , m_newValue(newValue)
{
}

void LayerPropertyCommand::undo()
{
    if (m_layerManager) {
        switch (m_property) {
        case Visibility:
            m_layerManager->setLayerVisible(m_layerIndex, m_oldValue.toBool());
            break;
        case Opacity:
            m_layerManager->setLayerOpacity(m_layerIndex, m_oldValue.toInt());
            break;
        case BlendMode:
            m_layerManager->setLayerBlendMode(m_layerIndex, m_oldValue.toString());
            break;
        case Name:
            m_layerManager->setLayerName(m_layerIndex, m_oldValue.toString());
            break;
        }
    }
}

void LayerPropertyCommand::redo()
{
    if (m_layerManager) {
        switch (m_property) {
        case Visibility:
            m_layerManager->setLayerVisible(m_layerIndex, m_newValue.toBool());
            break;
        case Opacity:
            m_layerManager->setLayerOpacity(m_layerIndex, m_newValue.toInt());
            break;
        case BlendMode:
            m_layerManager->setLayerBlendMode(m_layerIndex, m_newValue.toString());
            break;
        case Name:
            m_layerManager->setLayerName(m_layerIndex, m_newValue.toString());
            break;
        }
    }
}
