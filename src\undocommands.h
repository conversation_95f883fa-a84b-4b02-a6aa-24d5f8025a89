#ifndef UNDOCOMMANDS_H
#define UNDOCOMMANDS_H

#include <QUndoCommand>
#include <QPixmap>
#include <QRect>
#include <QColor>

class ImageCanvas;
class LayerManager;

// Base class for image editing commands
class ImageCommand : public QUndoCommand
{
public:
    explicit ImageCommand(ImageCanvas *canvas, const QString &text, QUndoCommand *parent = nullptr);

protected:
    ImageCanvas *m_canvas;
};

// Command for brightness/contrast adjustments
class BrightnessContrastCommand : public ImageCommand
{
public:
    BrightnessContrastCommand(ImageCanvas *canvas, int brightness, int contrast, 
                             const QPixmap &oldImage, QUndoCommand *parent = nullptr);

    void undo() override;
    void redo() override;

private:
    int m_brightness;
    int m_contrast;
    QPixmap m_oldImage;
    QPixmap m_newImage;
    bool m_firstRedo;
};

// Command for saturation adjustments
class SaturationCommand : public ImageCommand
{
public:
    SaturationCommand(ImageCanvas *canvas, int saturation, 
                     const QPixmap &oldImage, QUndoCommand *parent = nullptr);

    void undo() override;
    void redo() override;

private:
    int m_saturation;
    QPixmap m_oldImage;
    QPixmap m_newImage;
    bool m_firstRedo;
};

// Command for filter applications
class FilterCommand : public ImageCommand
{
public:
    FilterCommand(ImageCanvas *canvas, const QString &filterType, 
                 const QPixmap &oldImage, QUndoCommand *parent = nullptr);

    void undo() override;
    void redo() override;

private:
    QString m_filterType;
    QPixmap m_oldImage;
    QPixmap m_newImage;
    bool m_firstRedo;
};

// Command for crop operations
class CropCommand : public ImageCommand
{
public:
    CropCommand(ImageCanvas *canvas, const QRect &cropRect, 
               const QPixmap &oldImage, QUndoCommand *parent = nullptr);

    void undo() override;
    void redo() override;

private:
    QRect m_cropRect;
    QPixmap m_oldImage;
    QPixmap m_newImage;
    bool m_firstRedo;
};

// Command for resize operations
class ResizeCommand : public ImageCommand
{
public:
    ResizeCommand(ImageCanvas *canvas, const QSize &newSize, 
                 const QPixmap &oldImage, QUndoCommand *parent = nullptr);

    void undo() override;
    void redo() override;

private:
    QSize m_newSize;
    QSize m_oldSize;
    QPixmap m_oldImage;
    QPixmap m_newImage;
    bool m_firstRedo;
};

// Command for paint operations
class PaintCommand : public ImageCommand
{
public:
    PaintCommand(ImageCanvas *canvas, const QPixmap &oldImage, 
                const QPixmap &newImage, QUndoCommand *parent = nullptr);

    void undo() override;
    void redo() override;

private:
    QPixmap m_oldImage;
    QPixmap m_newImage;
};

// Layer commands
class LayerCommand : public QUndoCommand
{
public:
    explicit LayerCommand(LayerManager *layerManager, const QString &text, QUndoCommand *parent = nullptr);

protected:
    LayerManager *m_layerManager;
};

// Command for adding layers
class AddLayerCommand : public LayerCommand
{
public:
    AddLayerCommand(LayerManager *layerManager, const QString &layerName, 
                   const QPixmap &pixmap, QUndoCommand *parent = nullptr);

    void undo() override;
    void redo() override;

private:
    QString m_layerName;
    QPixmap m_pixmap;
    int m_layerIndex;
    bool m_firstRedo;
};

// Command for removing layers
class RemoveLayerCommand : public LayerCommand
{
public:
    RemoveLayerCommand(LayerManager *layerManager, int layerIndex, QUndoCommand *parent = nullptr);

    void undo() override;
    void redo() override;

private:
    int m_layerIndex;
    QString m_layerName;
    QPixmap m_pixmap;
    bool m_visible;
    int m_opacity;
    QString m_blendMode;
    bool m_firstRedo;
};

// Command for layer property changes
class LayerPropertyCommand : public LayerCommand
{
public:
    enum PropertyType {
        Visibility,
        Opacity,
        BlendMode,
        Name
    };

    LayerPropertyCommand(LayerManager *layerManager, int layerIndex, 
                        PropertyType property, const QVariant &oldValue, 
                        const QVariant &newValue, QUndoCommand *parent = nullptr);

    void undo() override;
    void redo() override;

private:
    int m_layerIndex;
    PropertyType m_property;
    QVariant m_oldValue;
    QVariant m_newValue;
};

#endif // UNDOCOMMANDS_H
